// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:device_info_plus/device_info_plus.dart' as _i833;
import 'package:dio/dio.dart' as _i361;
import 'package:flutter_secure_storage/flutter_secure_storage.dart' as _i558;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart'
    as _i161;

import '../../common/blocs/inline_form/inline_form_cubit.dart' as _i42;
import '../../src/advice/advice.dart' as _i683;
import '../../src/advice/data/remote/advice_remote_datasource.dart' as _i272;
import '../../src/advice/data/repositories/advice_repository_impl.dart'
    as _i666;
import '../../src/advice/domain/repository/advice_repository.dart' as _i686;
import '../../src/advice/domain/usecase/get_advice_detail.dart' as _i630;
import '../../src/advice/domain/usecase/get_advices.dart' as _i1033;
import '../../src/advice/presentation/bloc/advices/advices_bloc.dart' as _i619;
import '../../src/advice/presentation/bloc/get_advice_detail/get_advice_detail_cubit.dart'
    as _i985;
import '../../src/authentication/authentication.dart' as _i883;
import '../../src/authentication/data/local/authentication_local_datasource.dart'
    as _i314;
import '../../src/authentication/data/local/token_storage.dart' as _i893;
import '../../src/authentication/data/remote/authentication_remote_datasource.dart'
    as _i805;
import '../../src/authentication/data/remote/fcm_token_remote_datasource.dart'
    as _i180;
import '../../src/authentication/data/repositories/authentication_repository_impl.dart'
    as _i49;
import '../../src/authentication/data/repositories/fcm_token_repository_impl.dart'
    as _i40;
import '../../src/authentication/data/repositories/token_repository_impl.dart'
    as _i971;
import '../../src/authentication/domain/repository/authentication_repository.dart'
    as _i300;
import '../../src/authentication/domain/repository/fcm_token_repository.dart'
    as _i510;
import '../../src/authentication/domain/repository/token_repository.dart'
    as _i520;
import '../../src/authentication/domain/usecases/get_user.dart' as _i543;
import '../../src/authentication/domain/usecases/set_fcm_token.dart' as _i319;
import '../../src/authentication/domain/usecases/user_forgot_fassword.dart'
    as _i587;
import '../../src/authentication/domain/usecases/user_login.dart' as _i661;
import '../../src/authentication/domain/usecases/user_reset_password.dart'
    as _i291;
import '../../src/authentication/domain/usecases/user_signout.dart' as _i240;
import '../../src/authentication/presentation/bloc/authenticated/authenticated_cubit.dart'
    as _i545;
import '../../src/authentication/presentation/bloc/forgot_password/forgot_password_cubit.dart'
    as _i250;
import '../../src/authentication/presentation/bloc/login/login_cubit.dart'
    as _i588;
import '../../src/authentication/presentation/bloc/reset_password/reset_password_cubit.dart'
    as _i799;
import '../../src/authentication/presentation/bloc/sign_out/sign_out_cubit.dart'
    as _i979;
import '../../src/companion/companion/companion.dart' as _i241;
import '../../src/companion/companion/data/local/companion_local_datasource.dart'
    as _i482;
import '../../src/companion/companion/data/remote/companion_remote_datasource.dart'
    as _i331;
import '../../src/companion/companion/data/repository/companion_repository_impl.dart'
    as _i283;
import '../../src/companion/companion/domain/repository/companion_repository.dart'
    as _i233;
import '../../src/companion/companion/domain/usecase/get_companion.dart'
    as _i694;
import '../../src/companion/companion/domain/usecase/get_companion_list.dart'
    as _i91;
import '../../src/companion/companion/presentation/blocs/companion_show/companion_show_cubit.dart'
    as _i290;
import '../../src/companion/companion/presentation/blocs/companions_list/companions_list_bloc.dart'
    as _i567;
import '../../src/companion/followup/data/local/followup_local_datasource.dart'
    as _i261;
import '../../src/companion/followup/data/remote/followup_remote_datasource.dart'
    as _i647;
import '../../src/companion/followup/data/repository/followup_repository_impl.dart'
    as _i457;
import '../../src/companion/followup/domain/repository/followup_repository.dart'
    as _i346;
import '../../src/companion/followup/domain/usecase/create_followup.dart'
    as _i1024;
import '../../src/companion/followup/domain/usecase/delete_followup.dart'
    as _i798;
import '../../src/companion/followup/domain/usecase/get_followup.dart' as _i606;
import '../../src/companion/followup/domain/usecase/get_followup_list.dart'
    as _i848;
import '../../src/companion/followup/domain/usecase/update_followup.dart'
    as _i593;
import '../../src/companion/followup/presentation/blocs/create_followup/create_followup_cubit.dart'
    as _i543;
import '../../src/companion/followup/presentation/blocs/update_followup/update_followup_cubit.dart'
    as _i808;
import '../../src/companion/relapse/data/local/relapse_local_datasource.dart'
    as _i407;
import '../../src/companion/relapse/data/remote/relapse_remote_datasource.dart'
    as _i422;
import '../../src/companion/relapse/data/repository/relapse_repository_impl.dart'
    as _i640;
import '../../src/companion/relapse/domain/repository/relapse_repository.dart'
    as _i276;
import '../../src/companion/relapse/domain/usecase/create_relapse.dart'
    as _i244;
import '../../src/companion/relapse/domain/usecase/delete_relapse.dart'
    as _i463;
import '../../src/companion/relapse/domain/usecase/get_relapse.dart' as _i984;
import '../../src/companion/relapse/domain/usecase/update_relapse.dart'
    as _i354;
import '../../src/manager/health_center/data/local/health_center_local_datasource.dart'
    as _i726;
import '../../src/manager/health_center/data/remote/health_center_remote_datasource.dart'
    as _i70;
import '../../src/manager/health_center/data/repository/health_center_repository_impl.dart'
    as _i775;
import '../../src/manager/health_center/domain/repository/health_center_repository.dart'
    as _i709;
import '../../src/manager/health_center/domain/usecase/get_health_centers.dart'
    as _i517;
import '../../src/manager/health_center/presentation/blocs/health_center/health_center_cubit.dart'
    as _i429;
import '../../src/manager/health_center/presentation/blocs/health_center_drawer/health_center_drawer_cubit.dart'
    as _i636;
import '../../src/manager/instance/data/local/instance_local_datasource.dart'
    as _i21;
import '../../src/manager/instance/data/remote/instance_remote_datasource.dart'
    as _i664;
import '../../src/manager/instance/data/repository/instance_repository_impl.dart'
    as _i814;
import '../../src/manager/instance/domain/repository/instance_repository.dart'
    as _i678;
import '../../src/manager/instance/domain/usecase/create_instance.dart'
    as _i177;
import '../../src/manager/instance/domain/usecase/delete_instance.dart'
    as _i693;
import '../../src/manager/instance/domain/usecase/edit_instance.dart' as _i454;
import '../../src/manager/instance/domain/usecase/get_all_instance.dart'
    as _i306;
import '../../src/manager/instance/domain/usecase/get_instance_detail.dart'
    as _i235;
import '../../src/manager/instance/domain/usecase/get_instances.dart' as _i377;
import '../../src/manager/instance/domain/usecase/get_instances_relapsed.dart'
    as _i1007;
import '../../src/manager/instance/presentation/blocs/all_instances/all_instances_bloc.dart'
    as _i222;
import '../../src/manager/instance/presentation/blocs/create_instance/create_instance_cubit.dart'
    as _i197;
import '../../src/manager/instance/presentation/blocs/delete_instance/delete_instance_cubit.dart'
    as _i1069;
import '../../src/manager/instance/presentation/blocs/edit_instance/edit_instance_cubit.dart'
    as _i52;
import '../../src/manager/instance_features/companion/data/local/instance_companion_local_datasource.dart'
    as _i481;
import '../../src/manager/instance_features/companion/data/remote/companion_remote_datasource.dart'
    as _i246;
import '../../src/manager/instance_features/companion/data/repository/companion_repository_impl.dart'
    as _i116;
import '../../src/manager/instance_features/companion/domain/repository/companion_repository.dart'
    as _i510;
import '../../src/manager/instance_features/companion/domain/usecase/create_companion.dart'
    as _i330;
import '../../src/manager/instance_features/companion/domain/usecase/delete_companion.dart'
    as _i832;
import '../../src/manager/instance_features/companion/domain/usecase/get_companion_list.dart'
    as _i346;
import '../../src/manager/instance_features/diagnostic/data/remote/diagnostic_remote_datasource.dart'
    as _i994;
import '../../src/manager/instance_features/diagnostic/data/repository/diagnostic_repository_impl.dart'
    as _i990;
import '../../src/manager/instance_features/diagnostic/domain/repository/diagnostic_repository.dart'
    as _i656;
import '../../src/manager/instance_features/diagnostic/domain/usecase/create_diagnostic.dart'
    as _i870;
import '../../src/manager/instance_features/diagnostic/domain/usecase/delete_diagnostic.dart'
    as _i316;
import '../../src/manager/instance_features/diagnostic/domain/usecase/edit_diagnostic.dart'
    as _i296;
import '../../src/manager/instance_features/diagnostic/domain/usecase/get_diagnostics.dart'
    as _i549;
import '../../src/manager/instance_features/followup/data/remote/followup_remote_datasource.dart'
    as _i188;
import '../../src/manager/instance_features/followup/data/repository/followup_repository_impl.dart'
    as _i102;
import '../../src/manager/instance_features/followup/domain/repository/followup_repository.dart'
    as _i823;
import '../../src/manager/instance_features/followup/domain/usecase/delete_followup.dart'
    as _i782;
import '../../src/manager/instance_features/followup/domain/usecase/get_followup.dart'
    as _i734;
import '../../src/manager/instance_features/followup/domain/usecase/get_followup_list.dart'
    as _i533;
import '../../src/manager/instance_features/relapse/data/local/relapse_local_datasource.dart'
    as _i215;
import '../../src/manager/instance_features/relapse/data/remote/relapse_remote_datasource.dart'
    as _i290;
import '../../src/manager/instance_features/relapse/data/repository/relapse_repository_impl.dart'
    as _i346;
import '../../src/manager/instance_features/relapse/domain/repository/relapse_repository.dart'
    as _i368;
import '../../src/manager/instance_features/relapse/domain/usecase/create_relapse.dart'
    as _i174;
import '../../src/manager/instance_features/relapse/domain/usecase/delete_relapse.dart'
    as _i482;
import '../../src/manager/instance_features/relapse/domain/usecase/get_relapse.dart'
    as _i49;
import '../../src/manager/instance_features/relapse/domain/usecase/update_relapse.dart'
    as _i1051;
import '../../src/manager/instance_features/treatment/data/local/treatment_local_datasource.dart'
    as _i840;
import '../../src/manager/instance_features/treatment/data/remote/treatment_remote_datasource.dart'
    as _i68;
import '../../src/manager/instance_features/treatment/data/repository/treatment_repository_impl.dart'
    as _i91;
import '../../src/manager/instance_features/treatment/domain/repository/treatment_repository.dart'
    as _i139;
import '../../src/manager/instance_features/treatment/domain/usecase/create_treatment.dart'
    as _i320;
import '../../src/manager/instance_features/treatment/domain/usecase/delete_treatment.dart'
    as _i231;
import '../../src/manager/instance_features/treatment/domain/usecase/edit_treatment.dart'
    as _i127;
import '../../src/manager/instance_features/treatment/domain/usecase/get_treatment_list.dart'
    as _i740;
import '../../src/manager/member/data/local/member_local_datasource.dart'
    as _i136;
import '../../src/manager/member/data/remote/member_remote_datasource.dart'
    as _i509;
import '../../src/manager/member/data/repository/member_repository_impl.dart'
    as _i724;
import '../../src/manager/member/domain/repository/member_repository.dart'
    as _i752;
import '../../src/manager/member/domain/usecase/attach_member.dart' as _i960;
import '../../src/manager/member/domain/usecase/create_member.dart' as _i372;
import '../../src/manager/member/domain/usecase/delete_member.dart' as _i20;
import '../../src/manager/member/domain/usecase/edit_member.dart' as _i791;
import '../../src/manager/member/domain/usecase/get_members.dart' as _i248;
import '../../src/manager/member/domain/usecase/search_attach_member.dart'
    as _i106;
import '../../src/manager/member/member.dart' as _i813;
import '../../src/manager/member/presentation/blocs/attach_member/attach_member_cubit.dart'
    as _i561;
import '../../src/manager/member/presentation/blocs/create_member/create_member_cubit.dart'
    as _i864;
import '../../src/manager/member/presentation/blocs/edit_member/edit_member_cubit.dart'
    as _i57;
import '../../src/manager/questionnaire/data/local/questionnaire_local_datasource.dart'
    as _i784;
import '../../src/manager/questionnaire/data/remote/questionnaire_remote_datasource.dart'
    as _i638;
import '../../src/manager/questionnaire/data/repository/questionnaire_repository_impl.dart'
    as _i1007;
import '../../src/manager/questionnaire/domain/repository/questionnaire_repository.dart'
    as _i44;
import '../../src/manager/questionnaire/domain/usecase/get_questionnaires.dart'
    as _i868;
import '../../src/manager/questionnaire/presentation/blocs/get_questionnaires/get_questionnaires_cubit.dart'
    as _i186;
import '../../src/sync/handlers/companion_followup_sync_handler.dart' as _i656;
import '../../src/sync/handlers/companion_relapse_sync_handler.dart' as _i705;
import '../../src/sync/handlers/instance_sync_handler.dart' as _i306;
import '../../src/sync/handlers/manager_companion_sync_handler.dart' as _i346;
import '../../src/sync/handlers/manager_relapse_sync_handler.dart' as _i944;
import '../../src/sync/handlers/manager_treatment_sync_handler.dart' as _i959;
import '../../src/sync/handlers/sync_handlers.dart' as _i324;
import '../../src/sync/sync_service.dart' as _i712;
import '../../src/user_notification/data/remote/user_notification_remote_datasource.dart'
    as _i967;
import '../../src/user_notification/data/repository/user_notification_repository_impl.dart'
    as _i324;
import '../../src/user_notification/domain/repository/user_notification_repository.dart'
    as _i759;
import '../../src/user_notification/domain/usecase/delete_all_notifications.dart'
    as _i1;
import '../../src/user_notification/domain/usecase/delete_notification.dart'
    as _i8;
import '../../src/user_notification/domain/usecase/get_all_notification.dart'
    as _i418;
import '../../src/user_notification/domain/usecase/get_unread_notifications.dart'
    as _i473;
import '../../src/user_notification/domain/usecase/mark_all_notifications_as_read.dart'
    as _i408;
import '../../src/user_notification/domain/usecase/mark_notification_as_read.dart'
    as _i952;
import '../../src/user_notification/presentation/blocs/delete_all_notifications/delete_all_notifications_cubit.dart'
    as _i144;
import '../../src/user_notification/presentation/blocs/delete_notification/delete_notification_cubit.dart'
    as _i459;
import '../../src/user_notification/presentation/blocs/get_all_notification/get_all_notification_bloc.dart'
    as _i885;
import '../../src/user_notification/presentation/blocs/get_unread_notifications/get_unread_notifications_bloc.dart'
    as _i460;
import '../../src/user_notification/presentation/blocs/mark_notification_as_read/mark_notification_as_read_cubit.dart'
    as _i265;
import '../http/dio/dio_register_module.dart' as _i961;
import '../network/connection_checker.dart' as _i1050;
import '../objectbox/objectbox.dart' as _i434;
import '../objectbox/register_module.dart' as _i707;
import '../utils/attachment_storage_service.dart' as _i73;
import '../utils/device_info.dart' as _i295;
import 'register_module.dart' as _i291;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final registerModule = _$RegisterModule();
    final registerObjectBoxModule = _$RegisterObjectBoxModule();
    final dioRegisterModule = _$DioRegisterModule();
    gh.factory<_i636.HealthCenterDrawerCubit>(
        () => _i636.HealthCenterDrawerCubit());
    gh.factory<_i42.InlineFormCubit<dynamic>>(
        () => _i42.InlineFormCubit<dynamic>());
    gh.singleton<_i558.FlutterSecureStorage>(
        () => registerModule.createSecureStorage());
    gh.singleton<_i833.DeviceInfoPlugin>(() => registerModule.deviceInfoPlus());
    await gh.singletonAsync<_i434.ObjectBox>(
      () => registerObjectBoxModule.objectBox(),
      preResolve: true,
    );
    gh.lazySingleton<_i161.InternetConnection>(
        () => registerModule.internetConnect());
    gh.factory<_i136.MemberLocalDataSource>(() =>
        _i136.MemberLocalDataSourceImpl(objectBox: gh<_i434.ObjectBox>()));
    gh.factory<_i73.AttachmentStorageService>(
        () => _i73.AttachmentStorageServiceImpl());
    gh.factory<_i21.InstanceLocalDataSource>(() =>
        _i21.InstanceLocalDataSourceImpl(objectBox: gh<_i434.ObjectBox>()));
    gh.factory<_i314.AuthenticationLocalDataSource>(() =>
        _i314.AuthenticationLocalDataSourceImpl(
            objectBox: gh<_i434.ObjectBox>()));
    gh.factory<_i407.RelapseLocalDataSource>(() =>
        _i407.RelapseLocalDataSourceImpl(objectBox: gh<_i434.ObjectBox>()));
    gh.factory<_i840.TreatmentLocalDataSource>(() =>
        _i840.TreatmentLocalDataSourceImpl(objectBox: gh<_i434.ObjectBox>()));
    gh.factory<_i215.InstanceRelapseLocalDataSource>(
        () => _i215.InstanceRelapseLocalDataSourceImpl(gh<_i434.ObjectBox>()));
    gh.factory<_i482.CompanionLocalDataSource>(() =>
        _i482.CompanionLocalDataSourceImpl(objectBox: gh<_i434.ObjectBox>()));
    gh.factory<_i481.InstanceCompanionLocalDataSource>(() =>
        _i481.InstanceCompanionLocalDataSourceImpl(
            objectBox: gh<_i434.ObjectBox>()));
    gh.factory<_i261.FollowupLocalDataSource>(() =>
        _i261.FollowupLocalDataSourceImpl(objectBox: gh<_i434.ObjectBox>()));
    gh.factory<_i784.QuestionnaireLocalDataSource>(() =>
        _i784.QuestionnaireLocalDataSourceImpl(
            objectBox: gh<_i434.ObjectBox>()));
    gh.factory<_i726.HealthCenterLocalDataSource>(() =>
        _i726.HealthCenterLocalDataSourceImpl(
            objectBox: gh<_i434.ObjectBox>()));
    gh.factory<_i893.TokenStorage>(
        () => _i893.TokenStorage(gh<_i558.FlutterSecureStorage>()));
    gh.lazySingleton<_i1050.ConnectionChecker>(
        () => _i1050.ConnectionCheckerImpl());
    gh.factory<_i361.BaseOptions>(
      () => dioRegisterModule.dioOptions,
      instanceName: 'DioOptions',
    );
    await gh.singletonAsync<_i361.Dio>(
      () => dioRegisterModule.getUnauthorizedDioClient(
          gh<_i361.BaseOptions>(instanceName: 'DioOptions')),
      instanceName: 'Unauthorized',
      preResolve: true,
    );
    gh.factory<_i295.DeviceInfo>(
        () => _i295.DeviceInfoImpl(plugin: gh<_i833.DeviceInfoPlugin>()));
    gh.singleton<_i520.TokenRepository>(
        () => _i971.TokenRepositoryImpl(gh<_i893.TokenStorage>()));
    gh.factory<_i272.AdviceRemoteDataSource>(() =>
        _i272.AdviceRemoteDataSourceImpl(
            httpClient: gh<_i361.Dio>(instanceName: 'Unauthorized')));
    await gh.singletonAsync<_i361.Dio>(
      () => dioRegisterModule.getAuthorizedDioClient(
        gh<_i361.BaseOptions>(instanceName: 'DioOptions'),
        gh<_i883.TokenRepository>(),
      ),
      preResolve: true,
    );
    gh.factory<_i994.DiagnosticRemoteDataSource>(
        () => _i994.DiagnosticRemoteDataSourceImpl(gh<_i361.Dio>()));
    gh.factory<_i664.InstanceRemoteDataSource>(
        () => _i664.InstanceRemoteDataSourceImpl(gh<_i361.Dio>()));
    gh.factory<_i967.UserNotificationRemoteDataSource>(
        () => _i967.UserNotificationRemoteDataSourceImpl(gh<_i361.Dio>()));
    gh.factory<_i188.FollowupRemoteDataSource>(
        () => _i188.FollowupRemoteDataSourceImpl(gh<_i361.Dio>()));
    gh.factory<_i759.UserNotificationRepository>(() =>
        _i324.UserNotificationRepositoryImpl(
            gh<_i967.UserNotificationRemoteDataSource>()));
    gh.factory<_i70.HealthCenterRemoteDataSource>(
        () => _i70.HealthCenterRemoteDataSourceImpl(gh<_i361.Dio>()));
    gh.factory<_i638.QuestionnaireRemoteDataSource>(
        () => _i638.QuestionnaireRemoteDataSourceImpl(gh<_i361.Dio>()));
    gh.factory<_i422.RelapseRemoteDataSource>(
        () => _i422.RelapseRemoteDataSourceImpl(gh<_i361.Dio>()));
    gh.factory<_i180.FcmTokenRemoteDataSource>(
        () => _i180.FcmTokenRemoteDataSourceImpl(gh<_i361.Dio>()));
    gh.factory<_i509.MemberRemoteDataSource>(
        () => _i509.MemberRemoteDataSourceImpl(httpClient: gh<_i361.Dio>()));
    gh.factory<_i686.AdviceRepository>(() => _i666.AdviceRepositoryImpl(
        remoteDataSource: gh<_i272.AdviceRemoteDataSource>()));
    gh.factory<_i44.QuestionnaireRepository>(
        () => _i1007.QuestionnaireRepositoryImpl(
              gh<_i638.QuestionnaireRemoteDataSource>(),
              gh<_i784.QuestionnaireLocalDataSource>(),
              gh<_i1050.ConnectionChecker>(),
            ));
    gh.factory<_i752.MemberRepository>(() => _i724.MemberRepositoryImpl(
          gh<_i509.MemberRemoteDataSource>(),
          gh<_i136.MemberLocalDataSource>(),
          gh<_i1050.ConnectionChecker>(),
        ));
    gh.factory<_i705.CompanionRelapseSyncHandler>(
        () => _i705.CompanionRelapseSyncHandler(
              localDataSource: gh<_i407.RelapseLocalDataSource>(),
              remoteDataSource: gh<_i422.RelapseRemoteDataSource>(),
            ));
    gh.factory<_i372.CreateMember>(
        () => _i372.CreateMember(gh<_i752.MemberRepository>()));
    gh.factory<_i248.GetMembers>(
        () => _i248.GetMembers(gh<_i752.MemberRepository>()));
    gh.factory<_i656.DiagnosticRepository>(() =>
        _i990.DiagnosticRepositoryImpl(gh<_i994.DiagnosticRemoteDataSource>()));
    gh.factory<_i823.FollowupRepository>(() =>
        _i102.FollowupRepositoryImpl(gh<_i188.FollowupRemoteDataSource>()));
    gh.factory<_i782.DeleteFollowup>(
        () => _i782.DeleteFollowup(gh<_i823.FollowupRepository>()));
    gh.factory<_i8.DeleteNotification>(
        () => _i8.DeleteNotification(gh<_i759.UserNotificationRepository>()));
    gh.factory<_i1.DeleteAllNotifications>(() =>
        _i1.DeleteAllNotifications(gh<_i759.UserNotificationRepository>()));
    gh.factory<_i408.MarkAllNotificationsAsRead>(() =>
        _i408.MarkAllNotificationsAsRead(
            gh<_i759.UserNotificationRepository>()));
    gh.factory<_i418.GetAllNotification>(
        () => _i418.GetAllNotification(gh<_i759.UserNotificationRepository>()));
    gh.factory<_i952.MarkNotificationAsRead>(() =>
        _i952.MarkNotificationAsRead(gh<_i759.UserNotificationRepository>()));
    gh.factory<_i473.GetUnreadNotifications>(() =>
        _i473.GetUnreadNotifications(gh<_i759.UserNotificationRepository>()));
    gh.factory<_i510.FcmTokenRepository>(() =>
        _i40.FcmTokenRepositoryImpl(gh<_i180.FcmTokenRemoteDataSource>()));
    gh.factory<_i805.AuthenticationRemoteDataSource>(
        () => _i805.AuthenticationRemoteDataSourceImpl(
              unauthorizedClient: gh<_i361.Dio>(instanceName: 'Unauthorized'),
              authorizedClient: gh<_i361.Dio>(),
              deviceInfo: gh<_i295.DeviceInfo>(),
            ));
    gh.factory<_i144.DeleteAllNotificationsCubit>(() =>
        _i144.DeleteAllNotificationsCubit(gh<_i1.DeleteAllNotifications>()));
    gh.factory<_i460.GetUnreadNotificationsBloc>(() =>
        _i460.GetUnreadNotificationsBloc(gh<_i473.GetUnreadNotifications>()));
    gh.factory<_i290.RelapseRemoteDataSource>(
        () => _i290.RelapseRemoteDataSourceImpl(gh<_i361.Dio>()));
    gh.factory<_i68.TreatmentRemoteDataSource>(
        () => _i68.TreatmentRemoteDataSourceImpl(gh<_i361.Dio>()));
    gh.factory<_i306.InstanceSyncHandler>(() => _i306.InstanceSyncHandler(
          localDataSource: gh<_i21.InstanceLocalDataSource>(),
          remoteDataSource: gh<_i664.InstanceRemoteDataSource>(),
        ));
    gh.factory<_i959.ManagerTreatmentSyncHandler>(
        () => _i959.ManagerTreatmentSyncHandler(
              localDataSource: gh<_i840.TreatmentLocalDataSource>(),
              remoteDataSource: gh<_i68.TreatmentRemoteDataSource>(),
              instanceLocalDataSource: gh<_i21.InstanceLocalDataSource>(),
              attachmentStorage: gh<_i73.AttachmentStorageService>(),
            ));
    gh.factory<_i106.SearchAttachMember>(
        () => _i106.SearchAttachMember(gh<_i752.MemberRepository>()));
    gh.factory<_i791.EditMember>(
        () => _i791.EditMember(gh<_i752.MemberRepository>()));
    gh.factory<_i960.AttachMember>(
        () => _i960.AttachMember(gh<_i752.MemberRepository>()));
    gh.factory<_i331.CompanionRemoteDataSource>(
        () => _i331.CompanionRemoteDataSourceImpl(gh<_i361.Dio>()));
    gh.factory<_i709.HealthCenterRepository>(
        () => _i775.HealthCenterRepositoryImpl(
              gh<_i70.HealthCenterRemoteDataSource>(),
              gh<_i726.HealthCenterLocalDataSource>(),
              gh<_i1050.ConnectionChecker>(),
            ));
    gh.factory<_i734.GetFollowup>(
        () => _i734.GetFollowup(repository: gh<_i823.FollowupRepository>()));
    gh.factory<_i533.GetFollowupList>(() =>
        _i533.GetFollowupList(repository: gh<_i823.FollowupRepository>()));
    gh.factory<_i647.FollowupRemoteDataSource>(
        () => _i647.FollowupRemoteDataSourceImpl(gh<_i361.Dio>()));
    gh.factory<_i316.DeleteDiagnostic>(
        () => _i316.DeleteDiagnostic(gh<_i656.DiagnosticRepository>()));
    gh.factory<_i246.CompanionRemoteDataSource>(
        () => _i246.CompanionRemoteDataSourceImpl(gh<_i361.Dio>()));
    gh.factory<_i459.DeleteNotificationCubit>(
        () => _i459.DeleteNotificationCubit(gh<_i8.DeleteNotification>()));
    gh.factory<_i346.ManagerCompanionSyncHandler>(
        () => _i346.ManagerCompanionSyncHandler(
              localDataSource: gh<_i481.InstanceCompanionLocalDataSource>(),
              remoteDataSource: gh<_i246.CompanionRemoteDataSource>(),
              instanceLocalDataSource: gh<_i21.InstanceLocalDataSource>(),
            ));
    gh.factory<_i864.CreateMemberCubit>(
        () => _i864.CreateMemberCubit(gh<_i372.CreateMember>()));
    gh.factory<_i870.CreateDiagnostic>(
        () => _i870.CreateDiagnostic(gh<_i656.DiagnosticRepository>()));
    gh.factory<_i300.AuthenticationRepository>(
        () => _i49.AuthenticationRepositoryImpl(
              tokenRepository: gh<_i883.TokenRepository>(),
              fcmTokenRepository: gh<_i510.FcmTokenRepository>(),
              remoteDataSource: gh<_i805.AuthenticationRemoteDataSource>(),
              localDataSource: gh<_i314.AuthenticationLocalDataSource>(),
            ));
    gh.factory<_i868.GetQuestionnaires>(
        () => _i868.GetQuestionnaires(gh<_i44.QuestionnaireRepository>()));
    gh.factory<_i186.GetQuestionnairesCubit>(
        () => _i186.GetQuestionnairesCubit(gh<_i868.GetQuestionnaires>()));
    gh.factory<_i233.CompanionRepository>(() => _i283.CompanionRepositoryImpl(
          gh<_i331.CompanionRemoteDataSource>(),
          gh<_i482.CompanionLocalDataSource>(),
          gh<_i1050.ConnectionChecker>(),
        ));
    gh.factory<_i630.GetAdviceDetail>(() =>
        _i630.GetAdviceDetail(adviceRepository: gh<_i686.AdviceRepository>()));
    gh.factory<_i1033.GetAdvices>(() =>
        _i1033.GetAdvices(adviceRepository: gh<_i686.AdviceRepository>()));
    gh.factory<_i561.AttachMemberCubit>(
        () => _i561.AttachMemberCubit(gh<_i960.AttachMember>()));
    gh.factory<_i57.EditMemberCubit>(
        () => _i57.EditMemberCubit(gh<_i791.EditMember>()));
    gh.factory<_i296.EditDiagnostic>(() =>
        _i296.EditDiagnostic(repository: gh<_i656.DiagnosticRepository>()));
    gh.factory<_i549.GetDiagnostics>(() =>
        _i549.GetDiagnostics(repository: gh<_i656.DiagnosticRepository>()));
    gh.factory<_i265.MarkNotificationAsReadCubit>(() =>
        _i265.MarkNotificationAsReadCubit(gh<_i952.MarkNotificationAsRead>()));
    gh.factory<_i20.DeleteMember>(() =>
        _i20.DeleteMember(memberRepository: gh<_i752.MemberRepository>()));
    gh.factory<_i656.CompanionFollowupSyncHandler>(
        () => _i656.CompanionFollowupSyncHandler(
              localDataSource: gh<_i261.FollowupLocalDataSource>(),
              remoteDataSource: gh<_i647.FollowupRemoteDataSource>(),
            ));
    gh.factory<_i885.GetAllNotificationBloc>(
        () => _i885.GetAllNotificationBloc(gh<_i418.GetAllNotification>()));
    gh.factory<_i291.UserResetPassword>(
        () => _i291.UserResetPassword(gh<_i300.AuthenticationRepository>()));
    gh.factory<_i240.UserSignOut>(
        () => _i240.UserSignOut(gh<_i300.AuthenticationRepository>()));
    gh.factory<_i661.UserLogin>(
        () => _i661.UserLogin(gh<_i300.AuthenticationRepository>()));
    gh.factory<_i587.UserForgotPassword>(
        () => _i587.UserForgotPassword(gh<_i300.AuthenticationRepository>()));
    gh.factory<_i543.GetUser>(
        () => _i543.GetUser(gh<_i300.AuthenticationRepository>()));
    gh.factory<_i799.ResetPasswordCubit>(
        () => _i799.ResetPasswordCubit(gh<_i291.UserResetPassword>()));
    gh.factory<_i319.SetFcmToken>(
        () => _i319.SetFcmToken(gh<_i510.FcmTokenRepository>()));
    gh.factory<_i694.GetCompanion>(
        () => _i694.GetCompanion(gh<_i233.CompanionRepository>()));
    gh.factory<_i91.GetCompanionList>(
        () => _i91.GetCompanionList(gh<_i233.CompanionRepository>()));
    gh.factory<_i517.GetHealthCenters>(
        () => _i517.GetHealthCenters(gh<_i709.HealthCenterRepository>()));
    gh.factory<_i944.ManagerRelapseSyncHandler>(
        () => _i944.ManagerRelapseSyncHandler(
              localDataSource: gh<_i215.InstanceRelapseLocalDataSource>(),
              remoteDataSource: gh<_i290.RelapseRemoteDataSource>(),
              instanceLocalDataSource: gh<_i21.InstanceLocalDataSource>(),
            ));
    gh.factory<_i567.CompanionsListBloc>(
        () => _i567.CompanionsListBloc(gh<_i91.GetCompanionList>()));
    gh.factory<_i250.ForgotPasswordCubit>(
        () => _i250.ForgotPasswordCubit(gh<_i587.UserForgotPassword>()));
    gh.factory<_i588.LoginCubit>(() => _i588.LoginCubit(gh<_i661.UserLogin>()));
    gh.factory<_i619.AdvicesBloc>(
        () => _i619.AdvicesBloc(gh<_i683.GetAdvices>()));
    gh.factory<_i985.GetAdviceDetailCubit>(
        () => _i985.GetAdviceDetailCubit(gh<_i630.GetAdviceDetail>()));
    gh.factory<_i429.HealthCenterCubit>(
        () => _i429.HealthCenterCubit(gh<_i517.GetHealthCenters>()));
    gh.factory<_i979.SignOutCubit>(
        () => _i979.SignOutCubit(gh<_i883.UserSignOut>()));
    gh.lazySingleton<_i712.SyncService>(() => _i712.SyncServiceImpl(
          connectionChecker: gh<_i1050.ConnectionChecker>(),
          instanceSyncHandler: gh<_i324.InstanceSyncHandler>(),
          companionFollowupSyncHandler:
              gh<_i324.CompanionFollowupSyncHandler>(),
          companionRelapseSyncHandler: gh<_i324.CompanionRelapseSyncHandler>(),
          managerCompanionSyncHandler: gh<_i324.ManagerCompanionSyncHandler>(),
          managerRelapseSyncHandler: gh<_i324.ManagerRelapseSyncHandler>(),
          managerTreatmentSyncHandler: gh<_i324.ManagerTreatmentSyncHandler>(),
        ));
    gh.factory<_i368.RelapseRepository>(() => _i346.RelapseRepositoryImpl(
          gh<_i290.RelapseRemoteDataSource>(),
          gh<_i215.InstanceRelapseLocalDataSource>(),
          gh<_i21.InstanceLocalDataSource>(),
          gh<_i1050.ConnectionChecker>(),
          gh<_i712.SyncService>(),
        ));
    gh.factory<_i290.CompanionShowCubit>(() => _i290.CompanionShowCubit(
          gh<String>(),
          getCompanionUseCase: gh<_i694.GetCompanion>(),
        ));
    gh.factory<_i49.GetRelapse>(
        () => _i49.GetRelapse(repository: gh<_i368.RelapseRepository>()));
    gh.factory<_i1051.UpdateRelapse>(
        () => _i1051.UpdateRelapse(repository: gh<_i368.RelapseRepository>()));
    gh.factory<_i482.DeleteRelapse>(
        () => _i482.DeleteRelapse(repository: gh<_i368.RelapseRepository>()));
    gh.factory<_i174.CreateRelapse>(
        () => _i174.CreateRelapse(repository: gh<_i368.RelapseRepository>()));
    gh.singleton<_i545.AuthenticatedCubit>(
        () => _i545.AuthenticatedCubit(gh<_i883.GetUser>()));
    gh.factory<_i678.InstanceRepository>(() => _i814.InstanceRepositoryImpl(
          remoteDataSource: gh<_i664.InstanceRemoteDataSource>(),
          localDataSource: gh<_i21.InstanceLocalDataSource>(),
          connectionChecker: gh<_i1050.ConnectionChecker>(),
          syncService: gh<_i712.SyncService>(),
        ));
    gh.factory<_i139.TreatmentRepository>(() => _i91.TreatmentRepositoryImpl(
          gh<_i68.TreatmentRemoteDataSource>(),
          gh<_i840.TreatmentLocalDataSource>(),
          gh<_i21.InstanceLocalDataSource>(),
          gh<_i1050.ConnectionChecker>(),
          gh<_i712.SyncService>(),
          gh<_i73.AttachmentStorageService>(),
        ));
    gh.factory<_i346.FollowupRepository>(() => _i457.FollowupRepositoryImpl(
          gh<_i647.FollowupRemoteDataSource>(),
          gh<_i261.FollowupLocalDataSource>(),
          gh<_i1050.ConnectionChecker>(),
          gh<_i712.SyncService>(),
        ));
    gh.factory<_i798.DeleteFollowup>(
        () => _i798.DeleteFollowup(gh<_i346.FollowupRepository>()));
    gh.factory<_i454.EditInstance>(
        () => _i454.EditInstance(gh<_i678.InstanceRepository>()));
    gh.factory<_i693.DeleteInstance>(
        () => _i693.DeleteInstance(gh<_i678.InstanceRepository>()));
    gh.factory<_i177.CreateInstance>(
        () => _i177.CreateInstance(gh<_i678.InstanceRepository>()));
    gh.factory<_i1069.DeleteInstanceCubit>(
        () => _i1069.DeleteInstanceCubit(gh<_i693.DeleteInstance>()));
    gh.factory<_i377.GetInstances>(
        () => _i377.GetInstances(repository: gh<_i678.InstanceRepository>()));
    gh.factory<_i1007.GetInstancesRelapsed>(() => _i1007.GetInstancesRelapsed(
        repository: gh<_i678.InstanceRepository>()));
    gh.factory<_i510.CompanionRepository>(() => _i116.CompanionRepositoryImpl(
          gh<_i246.CompanionRemoteDataSource>(),
          gh<_i481.InstanceCompanionLocalDataSource>(),
          gh<_i1050.ConnectionChecker>(),
          gh<_i712.SyncService>(),
          gh<_i21.InstanceLocalDataSource>(),
          gh<_i813.MemberLocalDataSource>(),
        ));
    gh.factory<_i276.RelapseRepository>(() => _i640.RelapseRepositoryImpl(
          remoteDataSource: gh<_i422.RelapseRemoteDataSource>(),
          localDataSource: gh<_i407.RelapseLocalDataSource>(),
          companionLocalDataSource: gh<_i241.CompanionLocalDataSource>(),
          connectionChecker: gh<_i1050.ConnectionChecker>(),
          syncService: gh<_i712.SyncService>(),
        ));
    gh.factory<_i306.GetAllInstance>(
        () => _i306.GetAllInstance(gh<_i678.InstanceRepository>()));
    gh.factory<_i235.GetInstanceDetail>(
        () => _i235.GetInstanceDetail(gh<_i678.InstanceRepository>()));
    gh.factory<_i984.GetRelapse>(
        () => _i984.GetRelapse(repository: gh<_i276.RelapseRepository>()));
    gh.factory<_i354.UpdateRelapse>(
        () => _i354.UpdateRelapse(repository: gh<_i276.RelapseRepository>()));
    gh.factory<_i463.DeleteRelapse>(
        () => _i463.DeleteRelapse(repository: gh<_i276.RelapseRepository>()));
    gh.factory<_i244.CreateRelapse>(
        () => _i244.CreateRelapse(repository: gh<_i276.RelapseRepository>()));
    gh.factory<_i52.EditInstanceCubit>(
        () => _i52.EditInstanceCubit(gh<_i454.EditInstance>()));
    gh.factory<_i1024.CreateFollowup>(
        () => _i1024.CreateFollowup(gh<_i346.FollowupRepository>()));
    gh.factory<_i593.UpdateFollowup>(
        () => _i593.UpdateFollowup(gh<_i346.FollowupRepository>()));
    gh.factory<_i231.DeleteTreatment>(
        () => _i231.DeleteTreatment(gh<_i139.TreatmentRepository>()));
    gh.factory<_i740.GetTreatmentList>(
        () => _i740.GetTreatmentList(gh<_i139.TreatmentRepository>()));
    gh.factory<_i606.GetFollowup>(
        () => _i606.GetFollowup(repository: gh<_i346.FollowupRepository>()));
    gh.factory<_i848.GetFollowupList>(() =>
        _i848.GetFollowupList(repository: gh<_i346.FollowupRepository>()));
    gh.factory<_i320.CreateTreatment>(
        () => _i320.CreateTreatment(gh<_i139.TreatmentRepository>()));
    gh.factory<_i127.EditTreatment>(
        () => _i127.EditTreatment(gh<_i139.TreatmentRepository>()));
    gh.factory<_i543.CreateFollowupCubit>(() => _i543.CreateFollowupCubit(
          gh<_i1024.CreateFollowup>(),
          gh<_i1050.ConnectionChecker>(),
        ));
    gh.factory<_i197.CreateInstanceCubit>(() => _i197.CreateInstanceCubit(
          gh<_i177.CreateInstance>(),
          gh<_i1050.ConnectionChecker>(),
        ));
    gh.factory<_i808.UpdateFollowupCubit>(
        () => _i808.UpdateFollowupCubit(gh<_i593.UpdateFollowup>()));
    gh.factory<_i330.CreateCompanion>(
        () => _i330.CreateCompanion(gh<_i510.CompanionRepository>()));
    gh.factory<_i346.GetCompanionList>(
        () => _i346.GetCompanionList(gh<_i510.CompanionRepository>()));
    gh.factory<_i832.DeleteCompanion>(
        () => _i832.DeleteCompanion(gh<_i510.CompanionRepository>()));
    gh.factory<_i222.AllInstancesBloc>(
        () => _i222.AllInstancesBloc(gh<_i306.GetAllInstance>()));
    return this;
  }
}

class _$RegisterModule extends _i291.RegisterModule {}

class _$RegisterObjectBoxModule extends _i707.RegisterObjectBoxModule {}

class _$DioRegisterModule extends _i961.DioRegisterModule {}
