import 'package:injectable/injectable.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

abstract interface class ConnectionChecker {
  late final InternetConnection ic;

  Future<bool> isOnline();
}

@LazySingleton(as: ConnectionChecker)
class ConnectionCheckerImpl implements ConnectionChecker {
  @override
  late final InternetConnection ic;

  ConnectionCheckerImpl() {
    ic = InternetConnection();
  }

  @override
  Future<bool> isOnline() async {
    // return await InternetConnection().hasInternetAccess;
    return false;
  }
}
