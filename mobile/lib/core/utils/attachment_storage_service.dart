import 'dart:io';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

const localAttachmentPrefix = 'offline-attachment';

abstract class AttachmentStorageService {
  Future<String?> saveAttachment(String? filePath);
  Future<void> deleteAttachment(String fileName);
  Future<File?> getAttachment(String fileName);
  bool isOfflineAttachment(String fileName);
  Future<void> cleanupSyncedAttachments();
}

@Injectable(as: AttachmentStorageService)
class AttachmentStorageServiceImpl implements AttachmentStorageService {
  static const String _attachmentsDirName = 'attachments';

  Future<Directory> get _attachmentsDirectory async {
    final appDir = await getApplicationDocumentsDirectory();
    final attachmentsDir =
        Directory(path.join(appDir.path, _attachmentsDirName));

    if (!await attachmentsDir.exists()) {
      await attachmentsDir.create(recursive: true);
    }

    return attachmentsDir;
  }

  @override
  Future<String?> saveAttachment(String? filePath) async {
    if (filePath == null) return null;

    try {
      final sourceFile = File(filePath);
      if (!await sourceFile.exists()) {
        return null;
      }

      // Generate unique filename to avoid conflicts
      final extension = path.extension(filePath);
      final uniqueFileName =
          '$localAttachmentPrefix-${const Uuid().v4()}$extension';

      final attachmentsDir = await _attachmentsDirectory;
      final targetPath = path.join(attachmentsDir.path, uniqueFileName);

      // Copy file to permanent storage
      await sourceFile.copy(targetPath);

      // Return just the filename, not the full path
      return uniqueFileName;
    } catch (e) {
      // Log error but don't throw - return null to indicate failure
      return null;
    }
  }

  @override
  Future<void> deleteAttachment(String fileName) async {
    try {
      final attachmentsDir = await _attachmentsDirectory;
      final file = File(path.join(attachmentsDir.path, fileName));

      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      // Log error but don't throw
    }
  }

  @override
  Future<File?> getAttachment(String fileName) async {
    try {
      final attachmentsDir = await _attachmentsDirectory;
      final file = File(path.join(attachmentsDir.path, fileName));

      if (await file.exists()) {
        return file;
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> cleanupSyncedAttachments() async {
    // This would be called periodically to clean up attachments
    // that have been successfully synced to the server
    // Implementation depends on tracking which attachments are synced
  }

  @override
  bool isOfflineAttachment(String fileName) {
    return fileName.startsWith(localAttachmentPrefix);
  }
}
