import 'package:flutter/material.dart';
import 'package:s3g/common/common.dart';

class AppBarWithConnectionStatus extends StatelessWidget
    implements PreferredSizeWidget {
  final Widget? leading;
  final bool centerTitle;
  final Widget? title;
  final List<Widget>? actions;
  final bool showConnectionStatus;

  const AppBarWithConnectionStatus({
    super.key,
    this.leading,
    this.centerTitle = false,
    this.title,
    this.actions,
    this.showConnectionStatus = true,
  });

  @override
  Size get preferredSize =>
      Size.fromHeight(kToolbarHeight + (showConnectionStatus ? 50 : 0));

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        AppBar(
          leading: leading,
          centerTitle: centerTitle,
          title: title,
          actions: actions,
        ),
        if (showConnectionStatus)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: const Color(0xFF18181B), // Match AppBar dark theme
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 3,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Align(
              alignment: Alignment.centerLeft,
              child: ConnectionStatusIndicator(),
            ),
          ),
      ],
    );
  }
}
