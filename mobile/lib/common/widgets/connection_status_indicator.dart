import 'dart:async';

import 'package:flutter/material.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/src/sync/sync_service.dart';

class ConnectionStatusIndicator extends StatefulWidget {
  const ConnectionStatusIndicator({super.key});

  @override
  State<ConnectionStatusIndicator> createState() =>
      _ConnectionStatusIndicatorState();
}

class _ConnectionStatusIndicatorState extends State<ConnectionStatusIndicator>
    with SingleTickerProviderStateMixin {
  late final ConnectionChecker _connectionChecker;
  late final SyncService _syncService;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  StreamSubscription<InternetStatus>? _connectionSubscription;
  StreamSubscription<SyncStatus>? _syncStatusSubscription;

  InternetStatus _connectionStatus = InternetStatus.connected;
  SyncStatus _syncStatus = SyncStatus.synced;

  @override
  void initState() {
    super.initState();
    _connectionChecker = getIt<ConnectionChecker>();
    _syncService = getIt<SyncService>();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _setupSubscriptions();
    _checkInitialStatus();
  }

  void _setupSubscriptions() {
    _connectionSubscription =
        _connectionChecker.ic.onStatusChange.listen((status) {
      if (mounted) {
        setState(() {
          _connectionStatus = status;
        });
      }
    });

    _syncStatusSubscription = _syncService.syncStatusStream.listen((status) {
      if (mounted) {
        setState(() {
          _syncStatus = status;
          if (status == SyncStatus.syncing) {
            _animationController.repeat();
          } else {
            _animationController.stop();
            _animationController.value = 1.0;
          }
        });
      }
    });
  }

  Future<void> _checkInitialStatus() async {
    final isOnline = await _connectionChecker.isOnline();
    if (mounted) {
      setState(() {
        _connectionStatus =
            isOnline ? InternetStatus.connected : InternetStatus.disconnected;
      });
    }
  }

  @override
  void dispose() {
    _connectionSubscription?.cancel();
    _syncStatusSubscription?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  Color _getStatusColor() {
    if (_connectionStatus == InternetStatus.disconnected) {
      return Colors.grey;
    }

    switch (_syncStatus) {
      case SyncStatus.synced:
        return Colors.green;
      case SyncStatus.syncing:
        return Colors.orange;
      case SyncStatus.failed:
        return Colors.red;
      case SyncStatus.pending:
        return Colors.grey;
      case SyncStatus.deleted:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon() {
    if (_connectionStatus == InternetStatus.disconnected) {
      return Icons.wifi_off;
    }

    switch (_syncStatus) {
      case SyncStatus.synced:
        return Icons.cloud_done;
      case SyncStatus.syncing:
        return Icons.cloud_sync;
      case SyncStatus.failed:
        return Icons.cloud_off;
      case SyncStatus.pending:
        return Icons.cloud_queue;
      case SyncStatus.deleted:
        return Icons.cloud_off;
    }
  }

  String _getStatusText() {
    if (_connectionStatus == InternetStatus.disconnected) {
      return 'Hors ligne';
    }

    switch (_syncStatus) {
      case SyncStatus.synced:
        return 'Synchronisé';
      case SyncStatus.syncing:
        return 'Synchronisation...';
      case SyncStatus.failed:
        return 'Échec de synchronisation';
      case SyncStatus.pending:
        return 'En attente';
      case SyncStatus.deleted:
        return 'Supprimé';
    }
  }

  @override
  Widget build(BuildContext context) {
    final color = _getStatusColor();
    final icon = _getStatusIcon();
    final text = _getStatusText();

    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: color.withValues(
                alpha: 0.2), // Increased opacity for dark background
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: color.withValues(
                  alpha: 0.5), // Increased opacity for visibility
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 16,
                color: color.withValues(
                  alpha: _syncStatus == SyncStatus.syncing
                      ? _fadeAnimation.value
                      : 1.0,
                ),
              ),
              const SizedBox(width: 6),
              Text(
                text,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
