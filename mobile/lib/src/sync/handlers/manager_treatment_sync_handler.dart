import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/src/manager/instance/data/local/instance_local_datasource.dart';
import 'package:s3g/core/utils/attachment_storage_service.dart';
import 'package:s3g/src/manager/instance_features/treatment/data/local/models/treatment_local_model.dart';
import 'package:s3g/src/manager/instance_features/treatment/data/local/treatment_local_datasource.dart';
import 'package:s3g/src/manager/instance_features/treatment/data/remote/treatment_remote_datasource.dart';
import 'package:s3g/src/manager/instance_features/treatment/domain/entity/treatment.dart';
import 'package:s3g/src/sync/handlers/base_sync_handler.dart';
import 'package:s3g/src/sync/sync_service.dart';

@injectable
class ManagerTreatmentSyncHandler extends BaseSyncHandler {
  final TreatmentLocalDataSource _localDataSource;
  final TreatmentRemoteDataSource _remoteDataSource;
  final InstanceLocalDataSource _instanceLocalDataSource;
  final AttachmentStorageService _attachmentStorage;

  @override
  String get featureName => 'Manager Treatment';

  ManagerTreatmentSyncHandler({
    required TreatmentLocalDataSource localDataSource,
    required TreatmentRemoteDataSource remoteDataSource,
    required InstanceLocalDataSource instanceLocalDataSource,
    required AttachmentStorageService attachmentStorage,
  })  : _localDataSource = localDataSource,
        _remoteDataSource = remoteDataSource,
        _instanceLocalDataSource = instanceLocalDataSource,
        _attachmentStorage = attachmentStorage;

  @override
  Future<void> syncAll() async {
    final pendingTreatments = await _localDataSource.getPendingSyncTreatments();
    log('Found ${pendingTreatments.length} treatments to sync');

    for (final treatment in pendingTreatments) {
      await _syncSingleTreatment(treatment);
    }
  }

  Future<void> _syncSingleTreatment(TreatmentLocalModel treatment) async {
    try {
      // Get the instance to check if it's synced
      final instance = await _instanceLocalDataSource
          .getInstanceById(treatment.instanceLocalId);

      if (instance == null || instance.id == null) {
        log('Instance not found for treatment ${treatment.localId}');
        return;
      }

      // Skip if instance is not synced yet
      if (instance.syncStatus != SyncStatus.synced.value) {
        log('Instance ${instance.localId} not synced yet (status: ${instance.syncStatus}), skipping treatment ${treatment.localId}');
        return;
      }

      if (treatment.isDeleted) {
        // Handle deletion
        if (treatment.id != null) {
          try {
            await _remoteDataSource.deleteTreatment(
              instanceId: instance.id!,
              treatmentId: treatment.id!,
            );
          } on DioException catch (e) {
            if (e.response?.statusCode != 404) {
              rethrow;
            }
            // Ignore 404 errors - treatment already deleted on server
          }
        }

        // Remove from local after successful sync
        await _localDataSource.deleteTreatment(treatment.localId);
        log('Successfully deleted treatment ${treatment.localId}');
        return;
      }

      final hasOfflineAttachment = _attachmentStorage.isOfflineAttachment(
        treatment.attachment ?? '',
      );

      // Get attachment file path if exists
      String? attachmentPath;
      if (hasOfflineAttachment) {
        final attachmentFile =
            await _attachmentStorage.getAttachment(treatment.attachment!);
        attachmentPath = attachmentFile?.path;
      }

      // Handle creation
      if (treatment.isOfflineCreated) {
        // Create on server
        final response = await _remoteDataSource.createTreatment(
          instanceId: instance.id!,
          type: TreatmentType.values.firstWhere(
            (e) => e.name == treatment.type,
          ),
          observation: treatment.observation ?? '',
          attachment: attachmentPath,
        );

        // Update local with server ID
        await _localDataSource.markTreatmentAsSynced(
          treatment.localId,
          response.id,
        );

        log('Successfully created treatment ${treatment.localId} on server with id ${response.id}');
      } else {
        // Update on server
        await _remoteDataSource.editTreatment(
          instanceId: instance.id!,
          treatmentId: treatment.id!,
          type: TreatmentType.values.firstWhere(
            (e) => e.name == treatment.type,
          ),
          observation: treatment.observation ?? '',
          attachment: attachmentPath,
        );

        // Mark as synced
        treatment.updateSyncStatus(SyncStatus.synced);
        await _localDataSource.saveTreatment(treatment);

        log('Successfully updated treatment ${treatment.localId}');
      }

      // Clean up attachment after successful sync
      if (hasOfflineAttachment) {
        await _attachmentStorage.deleteAttachment(treatment.attachment!);
      }
    } on DioException catch (e) {
      if (e.response == null) {
        log('Failed to sync treatment ${treatment.localId}: $e');
        await _localDataSource.markTreatmentSyncFailed(
          treatment.localId,
          e.toString(),
        );
        return;
      }

      final response = e.response!;

      // Handle specific error case
      if (response.statusCode == 422 &&
          response.data.toString().contains('Le type a déjà été pris')) {
        log("Treatment type already taken, marking as synced ${treatment.localId}");
        await _localDataSource.deleteTreatment(treatment.localId);
      } else {
        await _localDataSource.markTreatmentSyncFailed(
          treatment.localId,
          e.toString(),
        );
      }
    } catch (e) {
      log('Failed to sync treatment ${treatment.localId}: $e');
      await _localDataSource.markTreatmentSyncFailed(
        treatment.localId,
        e.toString(),
      );
    }
  }
}
