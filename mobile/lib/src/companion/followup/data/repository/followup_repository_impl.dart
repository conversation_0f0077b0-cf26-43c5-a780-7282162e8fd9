import 'dart:developer';

import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/sync/sync_service.dart';
import 'package:s3g/src/companion/followup/data/local/followup_local_datasource.dart';
import 'package:s3g/src/companion/followup/data/local/models/followup_local_model.dart';
import 'package:s3g/src/companion/followup/domain/entity/followup.dart';

import '../../domain/repository/followup_repository.dart';
import '../remote/followup_remote_datasource.dart';

@Injectable(as: FollowupRepository)
class FollowupRepositoryImpl extends FollowupRepository {
  final FollowupRemoteDataSource _remoteDataSource;
  final FollowupLocalDataSource _localDataSource;
  final ConnectionChecker _connectionChecker;
  final SyncService _syncService;

  FollowupRepositoryImpl(
    this._remoteDataSource,
    this._localDataSource,
    this._connectionChecker,
    this._syncService,
  );

  @override
  RepositoryResponse<Followup> createFollowup({
    required String companionId,
    required String title,
    required String description,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to create on server first
        final response = await requestHelper(
          () => _remoteDataSource.createFollowup(
            companionId: companionId,
            title: title,
            description: description,
          ),
        );

        // If successful, save to local storage as synced
        if (response.isRight()) {
          final followup = response.getOrElse((l) => throw l);
          final localModel = FollowupLocalModel.fromEntity(
            followup,
            companionId: companionId,
          );
          await _localDataSource.updateFollowupFromServer(localModel);
        }

        return response;
      } else {
        // Device is offline, create locally
        log('Device offline, creating followup locally');

        final offlineFollowup = FollowupLocalModel.createOffline(
          title: title,
          description: description,
          companionId: companionId,
        );

        await _localDataSource.saveFollowup(offlineFollowup);

        // Return the entity
        return Right(offlineFollowup.toEntity());
      }
    } catch (e) {
      log('Error creating followup: $e');
      return Left(ServerFailure('Une erreur s\'est produite: ${e.toString()}'));
    }
  }

  @override
  RepositoryResponse<MessageResponse> deleteFollowup({
    required String companionId,
    required String followupId,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to delete on server first
        final response = await requestHelper(
          () => _remoteDataSource.deleteFollowup(
            companionId: companionId,
            followupId: followupId,
          ),
        );

        // If successful, delete from local storage
        if (response.isRight()) {
          await _localDataSource.deleteFollowupById(followupId);
        }

        return response;
      } else {
        // Device is offline, mark as deleted locally
        log('Device offline, marking followup as deleted locally');

        // Find the followup (by server ID or local ID)
        FollowupLocalModel? localFollowup =
            await _localDataSource.getFollowupById(followupId);

        if (localFollowup == null) {
          return Left(ServerFailure('Suivi introuvable'));
        }

        // Mark as deleted
        await _localDataSource.markFollowupAsDeleted(localFollowup.localId);

        return Right(MessageResponse(message: 'Followup deleted locally'));
      }
    } catch (e) {
      log('Error deleting followup: $e');
      return Left(ServerFailure(
          'Une erreur s\'est produite lors de la suppression: ${e.toString()}'));
    }
  }

  @override
  RepositoryResponse<Followup> getFollowup({
    required String companionId,
    required String followupId,
  }) async {
    try {
      // First, try to get from local storage
      FollowupLocalModel? localFollowup =
          await _localDataSource.getFollowupById(followupId);

      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to get fresh data from server
        final response = await requestHelper(
          () => _remoteDataSource.getFollowup(
            companionId: companionId,
            followupId: followupId,
          ),
        );

        // If successful, update local storage
        if (response.isRight()) {
          final followup = response.getOrElse((l) => throw l);

          final updatedLocalModel = FollowupLocalModel.fromEntity(
            followup,
            companionId: companionId,
          );
          updatedLocalModel.syncStatus = SyncStatus.synced.value;

          await _localDataSource.updateFollowupFromServer(updatedLocalModel);
        }

        return response;
      } else {
        // Device is offline, return from local storage
        if (localFollowup != null && !localFollowup.isDeleted) {
          return Right(localFollowup.toEntity());
        } else {
          return Left(
              ServerFailure('Suivi introuvable dans le stockage local'));
        }
      }
    } catch (e) {
      log('Error getting followup: $e');
      return Left(ServerFailure(
          'Une erreur s\'est produite lors de la récupération: ${e.toString()}'));
    }
  }

  @override
  RepositoryResponse<Paginated<Followup>> getFollowupList({
    required String companionId,
    int? page,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // First, sync any pending local changes to avoid overwriting them
        try {
          await _syncService.syncCompanionFollowups();
        } catch (e) {
          log('Warning: Failed to sync pending followups before fetch: $e');
          // Continue with fetch even if sync fails
        }

        // Try to get from server
        final response = await requestHelper(
          () => _remoteDataSource.getFollowupList(
            companionId: companionId,
            page: page,
          ),
        );

        // If successful and it's the first page, update local storage
        if (response.isRight() && (page == null || page == 1)) {
          final paginated = response.getOrElse((l) => throw l);

          // Update each followup properly to handle existing records
          final localFollowups = paginated.data
              .map((followup) => FollowupLocalModel.fromEntity(
                    followup,
                    companionId: companionId,
                  ))
              .toList();

          await _localDataSource.saveFollowups(localFollowups);
        }

        return response;
      } else {
        // Device is offline, get from local storage
        log('Device offline, getting followups from local storage');

        final localFollowups =
            await _localDataSource.getFollowupsByCompanion(companionId);

        final followups = localFollowups
            .where((f) => !f.isDeleted)
            .map((f) => f.toEntity())
            .toList();

        // Create a paginated response for offline mode
        final paginatedResponse = Paginated<Followup>.create(followups);

        return Right(paginatedResponse);
      }
    } catch (e) {
      log('Error getting followup list: $e');
      return Left(ServerFailure(
          'Une erreur s\'est produite lors de la récupération de la liste: ${e.toString()}'));
    }
  }

  @override
  RepositoryResponse<Followup> updateFollowup({
    required String companionId,
    required String followupId,
    required String title,
    required String description,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to update on server first
        final response = await requestHelper(
          () => _remoteDataSource.updateFollowup(
            companionId: companionId,
            followupId: followupId,
            title: title,
            description: description,
          ),
        );

        // If successful, update local storage
        if (response.isRight()) {
          final followup = response.getOrElse((l) => throw l);
          final localModel = FollowupLocalModel.fromEntity(
            followup,
            companionId: companionId,
          );
          localModel.syncStatus = SyncStatus.synced.value;
          await _localDataSource.updateFollowupFromServer(localModel);
        }

        return response;
      } else {
        // Device is offline, update locally
        log('Device offline, updating followup locally');

        // Find the followup
        FollowupLocalModel? localFollowup =
            await _localDataSource.getFollowupById(followupId);

        if (localFollowup == null) {
          return Left(ServerFailure('Suivi introuvable'));
        }

        // Update the content
        await _localDataSource.updateFollowupContent(
          localFollowup.localId,
          title: title,
          description: description,
        );

        // Get updated followup
        final updatedFollowup =
            await _localDataSource.getFollowupById(localFollowup.localId);

        if (updatedFollowup == null) {
          return Left(
              ServerFailure('Impossible de récupérer le suivi mis à jour'));
        }

        // Trigger sync in background
        _syncService.syncCompanionFollowups().catchError((e) {
          log('Failed to sync followup immediately: $e');
        });

        return Right(updatedFollowup.toEntity());
      }
    } catch (e) {
      log('Error updating followup: $e');
      return Left(ServerFailure(
          'Une erreur s\'est produite lors de la mise à jour: ${e.toString()}'));
    }
  }

  // Helper method to preload followups when online
  Future<void> preloadFollowups(String companionId) async {
    try {
      if (!await _connectionChecker.isOnline()) {
        return;
      }

      log('Preloading followups for companion: $companionId');

      final response = await getFollowupList(
        companionId: companionId,
        page: 1,
      );

      if (response.isRight()) {
        log('Successfully preloaded followups');
      }
    } catch (e) {
      log('Failed to preload followups: $e');
    }
  }
}
