import 'package:objectbox/objectbox.dart';
import 'package:s3g/src/companion/followup/domain/entity/followup.dart';
import 'package:s3g/src/sync/sync_service.dart' show SyncStatus;
import 'package:uuid/uuid.dart';

@Entity()
class FollowupLocalModel {
  @Id()
  int oid;

  @Index()
  @Unique()
  String? id; // Server ID, null for offline-created followups

  String title;
  String description;
  String companionId;
  String? instanceId; // Can be null for companion-only followups

  @Property(type: PropertyType.date)
  late DateTime createdAt;

  @Property(type: PropertyType.date)
  late DateTime updatedAt;

  String syncStatus; // SyncStatus enum as string

  @Index()
  @Unique()
  String localId; // Local UUID for offline followups

  @Property(type: PropertyType.date)
  DateTime? lastSyncAttempt;

  String? syncError;
  bool isDeleted;

  FollowupLocalModel({
    this.oid = 0,
    this.id,
    required this.title,
    required this.description,
    required this.companionId,
    this.instanceId,
    required this.createdAt,
    required this.updatedAt,
    required this.localId,
    this.syncStatus = 'pending',
    this.lastSyncAttempt,
    this.syncError,
    this.isDeleted = false,
  });

  // Convert from domain entity
  factory FollowupLocalModel.fromEntity(
    Followup followup, {
    required String companionId,
    String? instanceId,
  }) {
    return FollowupLocalModel(
      id: followup.id,
      localId: const Uuid().v4(),
      title: followup.title,
      description: followup.description,
      companionId: companionId,
      instanceId: instanceId,
      createdAt: followup.createdAt,
      updatedAt: followup.updatedAt,
      syncStatus: SyncStatus.synced.value,
    );
  }

  // Create offline followup
  factory FollowupLocalModel.createOffline({
    required String title,
    required String description,
    required String companionId,
    String? instanceId,
  }) {
    final now = DateTime.now();
    return FollowupLocalModel(
      title: title,
      localId: const Uuid().v4(),
      description: description,
      companionId: companionId,
      instanceId: instanceId,
      createdAt: now,
      updatedAt: now,
      syncStatus: SyncStatus.pending.value,
    );
  }

  // Convert to domain entity
  Followup toEntity() {
    return Followup(
      id: id ?? localId, // Use localId if no server ID
      title: title,
      description: description,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  // Update sync status
  void updateSyncStatus(SyncStatus status, {String? error}) {
    syncStatus = status.value;
    lastSyncAttempt = DateTime.now();
    syncError = error;
  }

  // Mark as synced with server ID
  void markAsSynced(String serverId) {
    id = serverId;
    syncStatus = SyncStatus.synced.value;
    lastSyncAttempt = DateTime.now();
    syncError = null;
  }

  // Check if needs sync
  bool get needsSync =>
      SyncStatus.fromString(syncStatus) == SyncStatus.pending ||
      SyncStatus.fromString(syncStatus) == SyncStatus.failed;

  // Check if is offline created
  bool get isOfflineCreated => id == null;

  // Get display ID
  String get displayId => id ?? localId;

  // Update content
  void updateContent({
    String? newTitle,
    String? newDescription,
  }) {
    if (newTitle != null) title = newTitle;
    if (newDescription != null) description = newDescription;
    updatedAt = DateTime.now();

    // Mark as needing sync if already synced
    if (SyncStatus.fromString(syncStatus) == SyncStatus.synced) {
      syncStatus = SyncStatus.pending.value;
    }
  }
}
