import 'dart:developer';

import 'package:injectable/injectable.dart' hide Order;
import 'package:s3g/core/objectbox/objectbox.dart';
import 'package:s3g/src/companion/followup/data/local/models/followup_local_model.dart';
import 'package:s3g/src/sync/sync_service.dart' show SyncStatus;

abstract class FollowupLocalDataSource {
  Future<List<FollowupLocalModel>> getAllFollowups();
  Future<List<FollowupLocalModel>> getFollowupsByCompanion(String companionId);
  Future<List<FollowupLocalModel>> getFollowupsByInstance(String instanceId);
  Future<List<FollowupLocalModel>> getPendingSyncFollowups();
  Future<FollowupLocalModel?> getFollowupById(String id);
  Future<List<FollowupLocalModel>> searchFollowups(String query,
      {String? companionId});
  Future<void> saveFollowup(FollowupLocalModel followup);
  Future<void> saveFollowups(List<FollowupLocalModel> followups);

  Future<void> deleteFollowup(String localId);
  Future<void> deleteFollowupById(String id);

  Future<void> clearAllFollowups();

  Future<void> markFollowupAsDeleted(String localId);
  Future<void> markFollowupAsDeletedById(String id);

  Future<void> markFollowupAsSynced(String localId, String serverId);
  Future<void> markFollowupSyncFailed(String localId, String error);

  Future<void> updateFollowupFromServer(FollowupLocalModel serverFollowup);
  Future<void> updateFollowupContent(String localId,
      {String? title, String? description});
}

@Injectable(as: FollowupLocalDataSource)
class FollowupLocalDataSourceImpl implements FollowupLocalDataSource {
  final ObjectBox objectBox;

  FollowupLocalDataSourceImpl({required this.objectBox});

  Box<FollowupLocalModel> get _box => objectBox.store.box<FollowupLocalModel>();

  @override
  Future<List<FollowupLocalModel>> getAllFollowups() async {
    final query = _box
        .query(FollowupLocalModel_.isDeleted.equals(false))
        .order(FollowupLocalModel_.createdAt, flags: Order.descending)
        .build();

    final followups = await query.findAsync();
    query.close();

    return followups;
  }

  @override
  Future<List<FollowupLocalModel>> getFollowupsByCompanion(
      String companionId) async {
    final query = _box
        .query(FollowupLocalModel_.companionId.equals(companionId) &
            FollowupLocalModel_.isDeleted.equals(false))
        .order(FollowupLocalModel_.createdAt, flags: Order.descending)
        .build();

    final followups = await query.findAsync();
    query.close();

    return followups;
  }

  @override
  Future<List<FollowupLocalModel>> getFollowupsByInstance(
      String instanceId) async {
    final query = _box
        .query(FollowupLocalModel_.instanceId.equals(instanceId) &
            FollowupLocalModel_.isDeleted.equals(false))
        .order(FollowupLocalModel_.createdAt, flags: Order.descending)
        .build();

    final followups = await query.findAsync();
    query.close();

    return followups;
  }

  @override
  Future<List<FollowupLocalModel>> getPendingSyncFollowups() async {
    final query = _box
        .query(
            (FollowupLocalModel_.syncStatus.equals(SyncStatus.pending.value) |
                FollowupLocalModel_.syncStatus.equals(SyncStatus.failed.value)))
        .build();

    final followups = await query.findAsync();
    query.close();

    return followups;
  }

  @override
  Future<FollowupLocalModel?> getFollowupById(String id) async {
    final query = _box
        .query(
          (FollowupLocalModel_.id.equals(id) |
                  FollowupLocalModel_.localId.equals(id)) &
              FollowupLocalModel_.isDeleted.equals(false),
        )
        .build();
    final followup = await query.findFirstAsync();
    query.close();
    return followup;
  }

  @override
  Future<void> saveFollowup(FollowupLocalModel followup) async {
    FollowupLocalModel? existing;

    // First, check if a followup with this server ID already exists
    if (followup.id != null) {
      final queryById =
          _box.query(FollowupLocalModel_.id.equals(followup.id!)).build();
      existing = await queryById.findFirstAsync();
      queryById.close();
    }

    // If not found by server ID, check by localId
    if (existing == null) {
      final queryByLocalId = _box
          .query(FollowupLocalModel_.localId.equals(followup.localId))
          .build();
      existing = await queryByLocalId.findFirstAsync();
      queryByLocalId.close();
    }

    if (existing != null) {
      // Update the existing record, preserving the ObjectBox ID
      existing.id = followup.id; // Update server ID if it was assigned
      existing.title = followup.title;
      existing.description = followup.description;
      existing.companionId = followup.companionId;
      existing.instanceId = followup.instanceId;
      existing.createdAt = followup.createdAt;
      existing.updatedAt = followup.updatedAt;
      existing.isDeleted = followup.isDeleted;
      existing.syncStatus = followup.syncStatus;
      existing.syncError = followup.syncError;
      existing.lastSyncAttempt = followup.lastSyncAttempt;
      await _box.putAsync(existing);
      return;
    }

    // No existing record found, save as new
    await _box.putAsync(followup);
  }

  @override
  Future<void> saveFollowups(List<FollowupLocalModel> followups) async {
    // Process each followup individually to handle updates
    for (final followup in followups) {
      await saveFollowup(followup);
    }
  }

  @override
  Future<void> markFollowupAsDeleted(String localId) async {
    final followup = await getFollowupById(localId);
    if (followup != null) {
      followup.isDeleted = true;
      followup.updatedAt = DateTime.now();
      followup.syncStatus = SyncStatus.pending.value;

      await saveFollowup(followup);
    }
  }

  @override
  Future<void> markFollowupAsDeletedById(String id) async {
    final followup = await getFollowupById(id);
    if (followup != null) {
      followup.isDeleted = true;
      followup.updatedAt = DateTime.now();
      followup.syncStatus = SyncStatus.pending.value;
      await saveFollowup(followup);
    }
  }

  @override
  Future<void> clearAllFollowups() async {
    await _box.removeAllAsync();
  }

  @override
  Future<List<FollowupLocalModel>> searchFollowups(String query,
      {String? companionId}) async {
    Condition<FollowupLocalModel> condition =
        FollowupLocalModel_.isDeleted.equals(false);

    if (companionId != null) {
      condition =
          condition & FollowupLocalModel_.companionId.equals(companionId);
    }

    // Search in title and description
    condition = condition &
        (FollowupLocalModel_.title.contains(query, caseSensitive: false) |
            FollowupLocalModel_.description
                .contains(query, caseSensitive: false));

    final builtQuery = _box
        .query(condition)
        .order(FollowupLocalModel_.createdAt, flags: Order.descending)
        .build();

    final followups = await builtQuery.findAsync();
    builtQuery.close();

    return followups;
  }

  // Helper methods for sync operations
  @override
  Future<void> markFollowupAsSynced(String localId, String serverId) async {
    final followup = await getFollowupById(localId);
    if (followup != null) {
      followup.markAsSynced(serverId);
      await saveFollowup(followup);
    }
  }

  @override
  Future<void> markFollowupSyncFailed(String localId, String error) async {
    final followup = await getFollowupById(localId);
    if (followup != null) {
      followup.updateSyncStatus(SyncStatus.failed, error: error);
      await saveFollowup(followup);
    }
  }

  @override
  Future<void> updateFollowupFromServer(
      FollowupLocalModel serverFollowup) async {
    // Check if we have a local version by server ID
    final existingById = serverFollowup.id != null
        ? await getFollowupById(serverFollowup.id!)
        : null;

    if (existingById != null) {
      // Check if local followup has pending changes
      if (existingById.needsSync) {
        // Local changes take priority - don't overwrite
        log('Followup ${existingById.id} has pending local changes, skipping server update');
        return;
      }

      // Only update if server version is newer
      if (serverFollowup.updatedAt.isAfter(existingById.updatedAt)) {
        // Update existing followup with server data, preserving the ObjectBox ID
        existingById.title = serverFollowup.title;
        existingById.description = serverFollowup.description;
        existingById.companionId = serverFollowup.companionId;
        existingById.instanceId = serverFollowup.instanceId;
        existingById.updatedAt = serverFollowup.updatedAt;
        existingById.syncStatus = SyncStatus.synced.value;
        existingById.syncError = null;
        await saveFollowup(existingById);
      }
    } else {
      // Check if we have any followup with the same ID (including deleted ones)
      final query = _box
          .query(
            FollowupLocalModel_.id.equals(serverFollowup.id!),
          )
          .build();

      final conflictingFollowup = await query.findFirstAsync();
      query.close();

      if (conflictingFollowup != null) {
        // Check if local followup has pending changes
        if (conflictingFollowup.needsSync) {
          // Local changes take priority - don't overwrite
          log('Followup ${conflictingFollowup.id} has pending local changes, skipping server update');
          return;
        }

        // Only update if server version is newer
        if (serverFollowup.updatedAt.isAfter(conflictingFollowup.updatedAt)) {
          // Update the existing followup (even if deleted) instead of creating a new one
          conflictingFollowup.title = serverFollowup.title;
          conflictingFollowup.description = serverFollowup.description;
          conflictingFollowup.companionId = serverFollowup.companionId;
          conflictingFollowup.instanceId = serverFollowup.instanceId;
          conflictingFollowup.createdAt = serverFollowup.createdAt;
          conflictingFollowup.updatedAt = serverFollowup.updatedAt;
          conflictingFollowup.syncStatus = SyncStatus.synced.value;
          conflictingFollowup.syncError = null;
          conflictingFollowup.isDeleted = false; // Resurrect if it was deleted
          await saveFollowup(conflictingFollowup);
        }
      } else {
        // Save new followup from server
        serverFollowup.syncStatus = SyncStatus.synced.value;
        await saveFollowup(serverFollowup);
      }
    }
  }

  @override
  Future<void> updateFollowupContent(
    String localId, {
    String? title,
    String? description,
  }) async {
    final followup = await getFollowupById(localId);
    if (followup != null) {
      followup.updateContent(
        newTitle: title,
        newDescription: description,
      );
      await saveFollowup(followup);
    }
  }

  @override
  Future<void> deleteFollowup(String localId) async {
    final query =
        _box.query(FollowupLocalModel_.localId.equals(localId)).build();

    final followup = await query.findFirstAsync();
    query.close();

    if (followup != null) {
      await _box.removeAsync(followup.oid);
    }
  }

  @override
  Future<void> deleteFollowupById(String id) async {
    final query = _box.query(FollowupLocalModel_.id.equals(id)).build();
    final followup = await query.findFirstAsync();
    query.close();

    if (followup != null) {
      await _box.removeAsync(followup.oid);
    }
  }
}
