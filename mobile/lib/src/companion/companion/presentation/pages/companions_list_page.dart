import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/companion/companion/domain/entity/companion.dart';

import '../blocs/companions_list/companions_list_bloc.dart';
import '../widgets/list_companion_item.dart';

class CompanionsListPage extends StatelessWidget {
  const CompanionsListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => getIt<CompanionsListBloc>()..add(PaginatedItemFetched()),
      child: BlocListener<RefreshDataBloc, RefreshDataState>(
        listener: (context, state) {
          if (state case RefreshDataCompanions()) {
            context
                .read<CompanionsListBloc>()
                .add(PaginatedItemFetched(refresh: true));
          }
        },
        child: const _CompanionPageContent(),
      ),
    );
  }
}

class _CompanionPageContent extends StatefulWidget {
  const _CompanionPageContent();

  @override
  State<_CompanionPageContent> createState() => _CompanionPageContentState();
}

class _CompanionPageContentState extends State<_CompanionPageContent> {
  late ScrollController _scrollController;

  @override
  void initState() {
    _scrollController = ScrollController();

    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppBarWithConnectionStatus(
        leading: AppBarLeadingTitle(),
        centerTitle: false,
        actions: [
          HelpIcon(),
          UserNotifications(),
          Padding(
            padding: EdgeInsets.only(right: 15, left: 3),
            child: UserIcon(),
          ),
        ],
      ),
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            context
                .read<CompanionsListBloc>()
                .add(PaginatedItemFetched(refresh: true));
          },
          child: SingleChildScrollView(
            padding: bodyPadding,
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const PageTitle(title: "Accompagnant"),

                // List of companions
                PaginatedWidget<CompanionsListBloc, Companion>(
                  scrollController: _scrollController,
                  render: (_, __, state) {
                    return ListCompanionItem(state: state);
                  } as WidgetRender<Companion>,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
