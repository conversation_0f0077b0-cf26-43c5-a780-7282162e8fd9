import 'package:injectable/injectable.dart';
import 'package:s3g/core/objectbox/objectbox.dart';
import 'package:s3g/src/authentication/data/local/models/user_local_model.dart';
import 'package:s3g/src/authentication/domain/entities/user.dart';

abstract class AuthenticationLocalDataSource {
  Future<UserLocalModel?> getUser();

  Future<void> setUser(User user);

  Future<void> deleteUser();
}

@Injectable(as: AuthenticationLocalDataSource)
class AuthenticationLocalDataSourceImpl
    implements AuthenticationLocalDataSource {
  final ObjectBox objectBox;

  AuthenticationLocalDataSourceImpl({required this.objectBox});

  Box<UserLocalModel> get _box => objectBox.store.box<UserLocalModel>();

  @override
  Future<UserLocalModel?> getUser() async {
    final users = await _box.getAllAsync();

    return users.firstOrNull;
  }

  @override
  Future<void> setUser(User user) async {
    // First check if a user with this ID already exists
    final query = _box.query(UserLocalModel_.id.equals(user.id)).build();
    final existingUser = await query.findFirstAsync();
    query.close();

    if (existingUser != null) {
      // Update the existing user
      existingUser.name = user.name;
      existingUser.role = user.role.name;
      existingUser.phone = user.phone;
      existingUser.email = user.email;
      existingUser.description = user.description;
      existingUser.createdAt = user.createdAt;
      existingUser.updatedAt = user.updatedAt;
      await _box.putAsync(existingUser);
    } else {
      // Remove all existing users first (since we only want one user)
      await _box.removeAllAsync();
      // Then add the new user
      await _box.putAsync(UserLocalModel.fromEntity(user));
    }
  }

  @override
  Future<void> deleteUser() async {
    final box = objectBox.store.box<UserLocalModel>();
    await box.removeAllAsync();
  }
}
