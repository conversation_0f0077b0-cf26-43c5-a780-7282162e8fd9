import 'dart:async';

import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/objectbox/objectbox.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/authentication/data/remote/authentication_remote_datasource.dart';
import 'package:s3g/src/authentication/domain/entities/login_response.dart';
import 'package:s3g/src/authentication/domain/repository/authentication_repository.dart';
import 'package:s3g/src/authentication/domain/repository/fcm_token_repository.dart';

import '../local/authentication_local_datasource.dart';

@Injectable(as: AuthenticationRepository)
class AuthenticationRepositoryImpl implements AuthenticationRepository {
  final FcmTokenRepository fcmTokenRepository;
  final TokenRepository tokenRepository;
  final AuthenticationRemoteDataSource remoteDataSource;
  final AuthenticationLocalDataSource localDataSource;

  AuthenticationRepositoryImpl({
    required this.tokenRepository,
    required this.fcmTokenRepository,
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<Either<Failure, Stream<User>>> getUser() async {
    return requestHelper(() async {
      final StreamController<User> userStream = StreamController();

      await tokenRepository.getAccessToken();

      localDataSource.getUser().then((userModel) {
        if (userModel != null && !userStream.isClosed) {
          userStream.add(userModel.toEntity());
        }
      });

      remoteDataSource.getUser().then((user) {
        localDataSource.setUser(user);

        if (!userStream.isClosed) {
          userStream.add(user);

          userStream.close();
        }
      });

      return userStream.stream;
    });
  }

  @override
  Future<Either<Failure, MessageResponse>> forgotPassword({
    required String phone,
  }) {
    return requestHelper(
      () => remoteDataSource.forgotPassword(phone: phone),
    );
  }

  @override
  Future<Either<Failure, LoginResponse>> login({
    required String phone,
    required String password,
  }) {
    return requestHelper(
      () {
        return remoteDataSource.login(phone: phone, password: password)
          ..then((value) {
            tokenRepository.saveToken(value.accessToken);
            localDataSource.setUser(value.user);
          });
      },
    );
  }

  @override
  Future<Either<Failure, MessageResponse>> resetPassword({
    required String phone,
    required String token,
    required String password,
  }) {
    return requestHelper(
      () => remoteDataSource.resetPassword(
        phone: phone,
        token: token,
        password: password,
      ),
    );
  }

  @override
  Future<Either<Failure, void>> signOut() {
    return requestHelper(() async {
      await Future.wait([
        fcmTokenRepository.deleteFcmToken(),
        tokenRepository.deleteToken(),
        localDataSource.deleteUser(),
        ObjectBox.clearAllData(),
      ]);
    });
  }
}
