import 'package:injectable/injectable.dart' hide Order;
import 'package:s3g/core/objectbox/objectbox.dart';
import 'package:s3g/src/manager/instance_features/companion/data/local/models/instance_companion_local_model.dart';
import 'package:s3g/src/sync/sync_service.dart';

abstract class InstanceCompanionLocalDataSource {
  Future<void> saveCompanion(InstanceCompanionLocalModel companion);
  Future<void> saveCompanions(List<InstanceCompanionLocalModel> companions);
  Future<InstanceCompanionLocalModel?> getCompanion(String id);
  Future<InstanceCompanionLocalModel?> getCompanionByLocalId(String localId);
  Future<List<InstanceCompanionLocalModel>> getAllCompanions();
  Future<List<InstanceCompanionLocalModel>> getCompanionsByInstance(
    String instanceLocalId,
  );
  Future<void> deleteCompanion(String id);
  Future<void> deleteCompanionById(String id);
  Future<void> deleteCompanionByLocalId(String localId);
  Future<void> markCompanionAsDeleted(String id);
  Future<void> markCompanionAsDeletedById(String id);
  Future<void> markCompanionAsDeletedByLocalId(String localId);
  Future<void> clearAll();
  Future<List<InstanceCompanionLocalModel>> getPendingSyncCompanions();
  Future<void> updateCompanion(InstanceCompanionLocalModel companion);
}

@Injectable(as: InstanceCompanionLocalDataSource)
class InstanceCompanionLocalDataSourceImpl
    implements InstanceCompanionLocalDataSource {
  final ObjectBox objectBox;

  InstanceCompanionLocalDataSourceImpl({required this.objectBox});

  Box<InstanceCompanionLocalModel> get _box =>
      objectBox.store.box<InstanceCompanionLocalModel>();

  @override
  Future<void> saveCompanion(InstanceCompanionLocalModel companion) async {
    InstanceCompanionLocalModel? existing;

    // First, check if a companion with this server ID already exists
    if (companion.id != null) {
      final queryById = _box
          .query(InstanceCompanionLocalModel_.id.equals(companion.id!))
          .build();
      existing = await queryById.findFirstAsync();
      queryById.close();
    }

    // If not found by server ID, check by localId
    if (existing == null) {
      final queryByLocalId = _box
          .query(InstanceCompanionLocalModel_.localId.equals(companion.localId))
          .build();
      existing = await queryByLocalId.findFirstAsync();
      queryByLocalId.close();
    }

    if (existing != null) {
      // Update the existing record, preserving the ObjectBox ID
      existing.id = companion.id; // Update server ID if it was assigned
      existing.type = companion.type;
      existing.userJson = companion.userJson;
      existing.updatedAt = companion.updatedAt;
      existing.syncStatus = companion.syncStatus;
      existing.lastSyncAttempt = companion.lastSyncAttempt;
      existing.syncError = companion.syncError;
      existing.isDeleted = companion.isDeleted;
      existing.instanceLocalId = companion.instanceLocalId;
      await _box.putAsync(existing);
      return;
    }

    // No existing record found, save as new
    await _box.putAsync(companion);
  }

  @override
  Future<void> saveCompanions(
      List<InstanceCompanionLocalModel> companions) async {
    for (final companion in companions) {
      await saveCompanion(companion);
    }
  }

  @override
  Future<InstanceCompanionLocalModel?> getCompanion(String id) async {
    final query = _box
        .query(
          InstanceCompanionLocalModel_.id.equals(id) |
              InstanceCompanionLocalModel_.localId.equals(id),
        )
        .build();
    final result = await query.findFirstAsync();
    query.close();
    return result;
  }

  @override
  Future<InstanceCompanionLocalModel?> getCompanionByLocalId(
      String localId) async {
    final query = _box
        .query(
          InstanceCompanionLocalModel_.localId.equals(localId),
        )
        .build();
    final result = await query.findFirstAsync();
    query.close();
    return result;
  }

  @override
  Future<List<InstanceCompanionLocalModel>> getAllCompanions() async {
    final query = _box
        .query(
          InstanceCompanionLocalModel_.isDeleted.equals(false),
        )
        .order(InstanceCompanionLocalModel_.createdAt, flags: Order.descending)
        .build();
    final result = await query.findAsync();
    query.close();
    return result;
  }

  @override
  Future<List<InstanceCompanionLocalModel>> getCompanionsByInstance(
      String instanceLocalId) async {
    final query = _box
        .query(
          InstanceCompanionLocalModel_.instanceLocalId.equals(instanceLocalId) &
              InstanceCompanionLocalModel_.isDeleted.equals(false),
        )
        .order(InstanceCompanionLocalModel_.createdAt, flags: Order.descending)
        .build();

    final result = await query.findAsync();
    query.close();

    return result;
  }

  @override
  Future<void> deleteCompanion(String id) async {
    final companion = await getCompanion(id);
    if (companion != null) {
      await _box.removeAsync(companion.oid);
    }
  }

  @override
  Future<void> deleteCompanionById(String id) async {
    final query =
        _box.query(InstanceCompanionLocalModel_.id.equals(id)).build();
    final companion = await query.findFirstAsync();
    query.close();

    if (companion != null) {
      await _box.removeAsync(companion.oid);
    }
  }

  @override
  Future<void> deleteCompanionByLocalId(String localId) async {
    final companion = await getCompanionByLocalId(localId);
    if (companion != null) {
      await _box.removeAsync(companion.oid);
    }
  }

  @override
  Future<void> markCompanionAsDeleted(String id) async {
    final companion = await getCompanion(id);
    if (companion != null) {
      companion.markAsDeleted();
      await saveCompanion(companion);
    }
  }

  @override
  Future<void> markCompanionAsDeletedById(String id) async {
    final query =
        _box.query(InstanceCompanionLocalModel_.id.equals(id)).build();
    final companion = await query.findFirstAsync();
    query.close();

    if (companion != null) {
      companion.markAsDeleted();
      await saveCompanion(companion);
    }
  }

  @override
  Future<void> markCompanionAsDeletedByLocalId(String localId) async {
    final companion = await getCompanionByLocalId(localId);
    if (companion != null) {
      companion.markAsDeleted();
      await saveCompanion(companion);
    }
  }

  @override
  Future<void> clearAll() async {
    await _box.removeAllAsync();
  }

  @override
  Future<List<InstanceCompanionLocalModel>> getPendingSyncCompanions() async {
    final query = _box
        .query(
          InstanceCompanionLocalModel_.syncStatus
                  .equals(SyncStatus.pending.value) |
              InstanceCompanionLocalModel_.syncStatus
                  .equals(SyncStatus.failed.value),
        )
        .build();
    final result = await query.findAsync();
    query.close();
    return result;
  }

  @override
  Future<void> updateCompanion(InstanceCompanionLocalModel companion) async {
    await saveCompanion(companion);
  }
}
