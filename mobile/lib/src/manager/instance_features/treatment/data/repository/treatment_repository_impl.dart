import 'dart:developer';

import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/manager/instance/data/local/instance_local_datasource.dart';
import 'package:s3g/core/utils/attachment_storage_service.dart';
import 'package:s3g/src/manager/instance_features/treatment/data/local/models/treatment_local_model.dart';
import 'package:s3g/src/manager/instance_features/treatment/data/local/treatment_local_datasource.dart';
import 'package:s3g/src/manager/instance_features/treatment/domain/entity/treatment.dart';
import 'package:s3g/src/sync/sync_service.dart';

import '../../domain/repository/treatment_repository.dart';
import '../remote/treatment_remote_datasource.dart';

@Injectable(as: TreatmentRepository)
class TreatmentRepositoryImpl extends TreatmentRepository {
  final TreatmentRemoteDataSource _remoteDataSource;
  final TreatmentLocalDataSource _localDataSource;
  final InstanceLocalDataSource _instanceLocalDataSource;
  final ConnectionChecker _connectionChecker;
  final SyncService _syncService;
  final AttachmentStorageService _attachmentStorage;

  TreatmentRepositoryImpl(
    this._remoteDataSource,
    this._localDataSource,
    this._instanceLocalDataSource,
    this._connectionChecker,
    this._syncService,
    this._attachmentStorage,
  );

  @override
  RepositoryResponse<Treatment> createTreatment({
    required String instanceId,
    required TreatmentType type,
    required String observation,
    required String? attachment,
  }) async {
    final isOnline = await _connectionChecker.isOnline();

    // Get instance local ID for storage
    final instanceModel =
        await _instanceLocalDataSource.getInstanceById(instanceId);

    if (isOnline) {
      // Try server creation
      final response = await requestHelper(
        () => _remoteDataSource.createTreatment(
          instanceId: instanceId,
          type: type,
          observation: observation,
          attachment: attachment,
        ),
      );

      // Save to local on success
      if (response.isRight() && instanceModel != null) {
        final treatment = response.getOrElse((l) => throw l);

        final localTreatment = TreatmentLocalModel.fromEntity(
          treatment,
          instanceLocalId: instanceModel.localId,
          observation: observation,
          attachment: attachment,
        );

        await _localDataSource.saveTreatment(localTreatment);
      }

      return response;
    } else {
      if (instanceModel == null) {
        return Left(CacheFailure('Instance introuvable'));
      }

      // Create offline
      return _createOfflineTreatment(
        type: type,
        instanceLocalId: instanceModel.localId,
        observation: observation,
        attachment: attachment,
      );
    }
  }

  Future<Either<Failure, Treatment>> _createOfflineTreatment({
    required String instanceLocalId,
    required TreatmentType type,
    required String observation,
    required String? attachment,
  }) async {
    try {
      // Save attachment to permanent storage if provided
      String? savedAttachment;
      if (attachment != null) {
        savedAttachment = await _attachmentStorage.saveAttachment(attachment);
      }

      final localTreatment = TreatmentLocalModel.createOffline(
        instanceLocalId: instanceLocalId,
        type: type,
        observation: observation,
        attachment: savedAttachment,
      );

      await _localDataSource.saveTreatment(localTreatment);

      log('Created offline treatment ${localTreatment.localId}');

      return Right(localTreatment.toEntity());
    } catch (e) {
      return const Left(DatabaseFailure('Erreur de base de données'));
    }
  }

  @override
  RepositoryResponse<MessageResponse> deleteTreatment({
    required String instanceId,
    required String treatmentId,
  }) async {
    final isOnline = await _connectionChecker.isOnline();

    // Check if treatment exists
    final treatmentLocal = await _localDataSource.getTreatmentById(treatmentId);

    if (isOnline) {
      final response = await requestHelper(
        () => _remoteDataSource.deleteTreatment(
          instanceId: instanceId,
          treatmentId: treatmentId,
        ),
      );

      // Delete locally on success
      if (response.isRight()) {
        await _localDataSource.deleteTreatmentById(treatmentId);

        if (treatmentLocal != null && treatmentLocal.attachment != null) {
          _attachmentStorage.deleteAttachment(treatmentLocal.attachment!);
        }
      }

      return response;
    } else {
      // Offline: mark as deleted
      await _localDataSource.markTreatmentAsDeletedById(treatmentId);

      // Delete attachment if it exists
      if (treatmentLocal != null && treatmentLocal.attachment != null) {
        _attachmentStorage.deleteAttachment(treatmentLocal.attachment!);
      }

      log('Marked treatment $treatmentId for deletion');

      return Right(
        MessageResponse(message: 'Marqué pour suppression hors ligne'),
      );
    }
  }

  @override
  RepositoryResponse<Treatment> editTreatment({
    required String instanceId,
    required String treatmentId,
    required TreatmentType type,
    required String observation,
    required String? attachment,
  }) async {
    final isOnline = await _connectionChecker.isOnline();

    // Get instance local ID for storage
    final instanceModel =
        await _instanceLocalDataSource.getInstanceById(instanceId);

    if (isOnline) {
      // Try server update
      final response = await requestHelper(
        () => _remoteDataSource.editTreatment(
          instanceId: instanceId,
          treatmentId: treatmentId,
          type: type,
          observation: observation,
          attachment: attachment,
        ),
      );

      // Update local on success
      if (response.isRight() && instanceModel != null) {
        final treatment = response.getOrElse((l) => throw l);
        final localTreatment = TreatmentLocalModel.fromEntity(
          treatment,
          instanceLocalId: instanceModel.localId,
          observation: observation,
          attachment: attachment,
        );

        await _localDataSource.saveTreatment(localTreatment);
      }

      return response;
    } else {
      // Update offline
      final localTreatment =
          await _localDataSource.getTreatmentById(treatmentId);

      if (localTreatment == null) {
        return const Left(ServerFailure('Traitement introuvable'));
      }

      return _updateOfflineTreatment(
        localTreatment: localTreatment,
        type: type,
        observation: observation,
        attachment: attachment,
      );
    }
  }

  Future<Either<Failure, Treatment>> _updateOfflineTreatment({
    required TreatmentLocalModel localTreatment,
    required TreatmentType type,
    required String observation,
    required String? attachment,
  }) async {
    try {
      // Save new attachment to permanent storage if provided
      String? savedAttachment = attachment;
      if (attachment != null) {
        savedAttachment = await _attachmentStorage.saveAttachment(attachment);

        // Delete old attachment if it exists and is different
        if (localTreatment.attachment != null &&
            localTreatment.attachment != savedAttachment) {
          await _attachmentStorage.deleteAttachment(localTreatment.attachment!);
        }
      }

      localTreatment.updateContent(
        newType: type,
        newObservation: observation,
        newAttachment: savedAttachment,
      );

      await _localDataSource.saveTreatment(localTreatment);

      log('Updated offline treatment ${localTreatment.localId}');

      return Right(localTreatment.toEntity());
    } catch (e) {
      return const Left(DatabaseFailure('Erreur de base de données'));
    }
  }

  @override
  RepositoryResponse<Treatment> getTreatment({
    required String instanceId,
    required String treatmentId,
  }) async {
    final isOnline = await _connectionChecker.isOnline();

    // Get instance local ID for storage
    final instanceModel =
        await _instanceLocalDataSource.getInstanceById(instanceId);

    if (isOnline) {
      // Try server fetch
      final response = await requestHelper(
        () => _remoteDataSource.getTreatment(
          instanceId: instanceId,
          treatmentId: treatmentId,
        ),
      );

      // Update local on success
      if (response.isRight() && instanceModel != null) {
        final treatment = response.getOrElse((l) => throw l);
        final updatedLocal = TreatmentLocalModel.fromEntity(
          treatment,
          instanceLocalId: instanceModel.localId,
          observation: treatment.observation,
          attachment: treatment.attachment,
        );

        await _localDataSource.updateTreatmentFromServer(updatedLocal);
      }

      return response;
    } else {
      // Offline: return local data
      final localTreatment =
          await _localDataSource.getTreatmentById(treatmentId);

      if (localTreatment == null) {
        return const Left(ServerFailure('Aucune donnée disponible hors ligne'));
      }

      return Right(localTreatment.toEntity());
    }
  }

  @override
  RepositoryResponse<List<Treatment>> getTreatmentList({
    required String instanceId,
  }) async {
    final isOnline = await _connectionChecker.isOnline();

    // Get instance local ID for storage
    final instanceModel =
        await _instanceLocalDataSource.getInstanceById(instanceId);

    if (isOnline) {
      try {
        await _syncService.syncManagerTreatments();
      } catch (e) {
        log('Failed to sync companions before fetch: $e');
        // Continue with fetch even if sync fails
      }

      // Try server fetch
      final response = await requestHelper(
        () => _remoteDataSource.getTreatmentList(
          instanceId: instanceId,
        ),
      );

      // Update local on success
      if (response.isRight() && instanceModel != null) {
        final treatments = response.getOrElse((l) => []);

        final localTreatments = treatments
            .map((treatment) => TreatmentLocalModel.fromEntity(
                  treatment,
                  instanceLocalId: instanceModel.localId,
                ))
            .toList();

        await _localDataSource.saveTreatments(localTreatments);
      }

      return response;
    } else {
      if (instanceModel == null) {
        return const Left(CacheFailure('Instance introuvable'));
      }

      // Offline: return local data
      final localTreatments = await _localDataSource
          .getTreatmentsByInstanceLocalId(instanceModel.localId);

      final treatments = localTreatments.map((t) => t.toEntity()).toList();

      log('Returned ${treatments.length} treatments from local storage');

      return Right(treatments);
    }
  }
}
