import 'package:injectable/injectable.dart' hide Order;
import 'package:s3g/core/objectbox/objectbox.dart';
import 'package:s3g/src/manager/instance_features/treatment/data/local/models/treatment_local_model.dart';
import 'package:s3g/src/sync/sync_service.dart';

abstract class TreatmentLocalDataSource {
  Future<void> saveTreatment(TreatmentLocalModel treatment);
  Future<void> saveTreatments(List<TreatmentLocalModel> treatments);
  Future<TreatmentLocalModel?> getTreatmentById(String id);
  Future<TreatmentLocalModel?> getTreatmentByLocalId(String localId);
  Future<List<TreatmentLocalModel>> getTreatmentsByInstanceLocalId(
      String instanceLocalId);
  Future<List<TreatmentLocalModel>> getAllTreatments();
  Future<void> updateTreatmentContent(
    String localId, {
    String? type,
    String? observation,
    String? attachment,
  });

  // Physical deletion methods
  Future<void> deleteTreatment(String localId);
  Future<void> deleteTreatmentById(String id);

  // Soft deletion methods
  Future<void> markTreatmentAsDeleted(String localId);
  Future<void> markTreatmentAsDeletedById(String id);

  // Sync helper methods
  Future<void> markTreatmentAsSynced(String localId, String serverId);
  Future<void> markTreatmentSyncFailed(String localId, String error);
  Future<List<TreatmentLocalModel>> getPendingSyncTreatments();
  Future<void> updateTreatmentFromServer(TreatmentLocalModel serverTreatment);
}

@Injectable(as: TreatmentLocalDataSource)
class TreatmentLocalDataSourceImpl implements TreatmentLocalDataSource {
  final ObjectBox objectBox;

  TreatmentLocalDataSourceImpl({required this.objectBox});

  Box<TreatmentLocalModel> get _box =>
      objectBox.store.box<TreatmentLocalModel>();

  @override
  Future<void> saveTreatment(TreatmentLocalModel treatment) async {
    TreatmentLocalModel? existing;

    // First, check if treatment with this server ID already exists
    if (treatment.id != null) {
      final queryById =
          _box.query(TreatmentLocalModel_.id.equals(treatment.id!)).build();
      existing = await queryById.findFirstAsync();
      queryById.close();
    }

    // If not found by server ID, check by localId
    if (existing == null) {
      final queryByLocalId = _box
          .query(TreatmentLocalModel_.localId.equals(treatment.localId))
          .build();
      existing = await queryByLocalId.findFirstAsync();
      queryByLocalId.close();
    }

    if (existing != null) {
      // Update existing record, preserving ObjectBox ID
      existing.id = treatment.id;
      existing.type = treatment.type;
      existing.observation = treatment.observation;
      existing.attachment = treatment.attachment;
      existing.syncStatus = treatment.syncStatus;
      existing.lastSyncAttempt = treatment.lastSyncAttempt;
      existing.syncError = treatment.syncError;
      existing.isDeleted = treatment.isDeleted;
      existing.updatedAt = treatment.updatedAt;
      await _box.putAsync(existing);
      return;
    }

    // No existing record found, save as new
    await _box.putAsync(treatment);
  }

  @override
  Future<void> saveTreatments(List<TreatmentLocalModel> treatments) async {
    for (final treatment in treatments) {
      await saveTreatment(treatment);
    }
  }

  @override
  Future<TreatmentLocalModel?> getTreatmentById(String id) async {
    final query = _box.query(TreatmentLocalModel_.id.equals(id)).build();
    final result = await query.findFirstAsync();
    query.close();
    return result;
  }

  @override
  Future<TreatmentLocalModel?> getTreatmentByLocalId(String localId) async {
    final query =
        _box.query(TreatmentLocalModel_.localId.equals(localId)).build();

    final result = await query.findFirstAsync();
    query.close();
    return result;
  }

  @override
  Future<List<TreatmentLocalModel>> getTreatmentsByInstanceLocalId(
      String instanceLocalId) async {
    final query = _box
        .query(TreatmentLocalModel_.instanceLocalId.equals(instanceLocalId) &
            TreatmentLocalModel_.isDeleted.equals(false))
        .order(TreatmentLocalModel_.createdAt, flags: Order.descending)
        .build();
    final results = await query.findAsync();
    query.close();
    return results;
  }

  @override
  Future<List<TreatmentLocalModel>> getAllTreatments() async {
    final query = _box
        .query(TreatmentLocalModel_.isDeleted.equals(false))
        .order(TreatmentLocalModel_.createdAt, flags: Order.descending)
        .build();
    final results = await query.findAsync();
    query.close();
    return results;
  }

  @override
  Future<void> updateTreatmentContent(
    String localId, {
    String? type,
    String? observation,
    String? attachment,
  }) async {
    final treatment = await getTreatmentByLocalId(localId);
    if (treatment == null) return;

    if (type != null) treatment.type = type;
    if (observation != null) treatment.observation = observation;
    if (attachment != null) treatment.attachment = attachment;
    treatment.updatedAt = DateTime.now();

    if (SyncStatus.fromString(treatment.syncStatus) == SyncStatus.synced) {
      treatment.syncStatus = SyncStatus.pending.value;
    }

    await saveTreatment(treatment);
  }

  @override
  Future<void> deleteTreatment(String localId) async {
    final query =
        _box.query(TreatmentLocalModel_.localId.equals(localId)).build();
    final treatment = await query.findFirstAsync();
    query.close();

    if (treatment != null) {
      await _box.removeAsync(treatment.oid);
    }
  }

  @override
  Future<void> deleteTreatmentById(String id) async {
    final query = _box.query(TreatmentLocalModel_.id.equals(id)).build();
    final treatment = await query.findFirstAsync();
    query.close();

    if (treatment != null) {
      await _box.removeAsync(treatment.oid);
    }
  }

  @override
  Future<void> markTreatmentAsDeleted(String localId) async {
    final treatment = await getTreatmentByLocalId(localId);
    if (treatment == null) return;

    treatment.markAsDeleted();
    await saveTreatment(treatment);
  }

  @override
  Future<void> markTreatmentAsDeletedById(String id) async {
    final treatment = await getTreatmentById(id);
    if (treatment == null) return;

    treatment.markAsDeleted();
    await saveTreatment(treatment);
  }

  @override
  Future<void> markTreatmentAsSynced(String localId, String serverId) async {
    final treatment = await getTreatmentByLocalId(localId);
    if (treatment == null) return;

    treatment.markAsSynced(serverId);
    await saveTreatment(treatment);
  }

  @override
  Future<void> markTreatmentSyncFailed(String localId, String error) async {
    final treatment = await getTreatmentByLocalId(localId);
    if (treatment == null) return;

    treatment.updateSyncStatus(SyncStatus.failed, error: error);
    await saveTreatment(treatment);
  }

  @override
  Future<List<TreatmentLocalModel>> getPendingSyncTreatments() async {
    // CRITICAL: Do NOT filter out deleted items!
    final query = _box
        .query(
            TreatmentLocalModel_.syncStatus.equals(SyncStatus.pending.value) |
                TreatmentLocalModel_.syncStatus.equals(SyncStatus.failed.value))
        .build();
    final results = await query.findAsync();
    query.close();
    return results;
  }

  @override
  Future<void> updateTreatmentFromServer(
      TreatmentLocalModel serverTreatment) async {
    final existing = await getTreatmentById(serverTreatment.id!);
    if (existing != null) {
      // Update with server data
      existing.type = serverTreatment.type;
      existing.observation = serverTreatment.observation;
      existing.attachment = serverTreatment.attachment;
      existing.syncStatus = SyncStatus.synced.value;
      existing.lastSyncAttempt = DateTime.now();
      existing.syncError = null;
      existing.updatedAt = serverTreatment.updatedAt;
      await saveTreatment(existing);
    } else {
      // Save new from server
      serverTreatment.syncStatus = SyncStatus.synced.value;
      await saveTreatment(serverTreatment);
    }
  }
}
