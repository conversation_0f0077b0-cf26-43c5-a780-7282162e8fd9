import 'package:objectbox/objectbox.dart';
import 'package:s3g/src/manager/instance_features/treatment/domain/entity/treatment.dart';
import 'package:s3g/src/manager/instance_features/treatment/data/remote/models/treatment_model.dart';
import 'package:s3g/src/sync/sync_service.dart';
import 'package:uuid/uuid.dart';

@Entity()
class TreatmentLocalModel {
  @Id()
  int oid = 0;

  @Index()
  @Unique()
  String? id;

  @Index()
  @Unique()
  String localId;

  @Index()
  String instanceLocalId;

  String type;
  String? observation;
  String? attachment;

  String syncStatus;
  DateTime? lastSyncAttempt;
  String? syncError;
  bool isDeleted;

  @Property(type: PropertyType.date)
  DateTime createdAt;

  @Property(type: PropertyType.date)
  DateTime updatedAt;

  TreatmentLocalModel({
    this.id,
    required this.localId,
    required this.instanceLocalId,
    required this.type,
    this.observation,
    this.attachment,
    required this.syncStatus,
    this.lastSyncAttempt,
    this.syncError,
    this.isDeleted = false,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get needsSync =>
      SyncStatus.fromString(syncStatus) == SyncStatus.pending ||
      SyncStatus.fromString(syncStatus) == SyncStatus.failed;

  bool get isOfflineCreated => id == null;

  String get displayId => id ?? localId;

  factory TreatmentLocalModel.fromEntity(
    Treatment treatment, {
    required String instanceLocalId,
    String? observation,
    String? attachment,
  }) {
    return TreatmentLocalModel(
      id: treatment.id,
      localId: const Uuid().v4(),
      instanceLocalId: instanceLocalId,
      type: treatment.type.name,
      observation: observation,
      attachment: attachment,
      syncStatus: SyncStatus.synced.value,
      createdAt: treatment.createdAt,
      updatedAt: treatment.updatedAt,
    );
  }

  factory TreatmentLocalModel.createOffline({
    required String instanceLocalId,
    required TreatmentType type,
    String? observation,
    String? attachment,
  }) {
    final now = DateTime.now();

    return TreatmentLocalModel(
      localId: const Uuid().v4(),
      instanceLocalId: instanceLocalId,
      type: type.name,
      observation: observation,
      attachment: attachment,
      syncStatus: SyncStatus.pending.value,
      createdAt: now,
      updatedAt: now,
    );
  }

  Treatment toEntity() {
    return Treatment(
      id: displayId,
      instanceId: instanceLocalId,
      type: TreatmentType.values.firstWhere(
        (e) => e.name == type,
        orElse: () => TreatmentType.MEDICAL_CARE,
      ),
      observation: observation ?? '',
      attachment: attachment,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  void updateSyncStatus(SyncStatus status, {String? error}) {
    syncStatus = status.value;
    lastSyncAttempt = DateTime.now();
    syncError = error;
  }

  void markAsSynced(String serverId) {
    id = serverId;
    syncStatus = SyncStatus.synced.value;
    lastSyncAttempt = DateTime.now();
    syncError = null;
  }

  void updateContent({
    TreatmentType? newType,
    String? newObservation,
    String? newAttachment,
  }) {
    if (newType != null) type = newType.name;
    if (newObservation != null) observation = newObservation;
    if (newAttachment != null) attachment = newAttachment;
    updatedAt = DateTime.now();

    if (SyncStatus.fromString(syncStatus) == SyncStatus.synced) {
      syncStatus = SyncStatus.pending.value;
    }
  }

  void markAsDeleted() {
    isDeleted = true;
    updatedAt = DateTime.now();

    if (SyncStatus.fromString(syncStatus) == SyncStatus.synced) {
      syncStatus = SyncStatus.pending.value;
    }
  }

  TreatmentModel toRemoteModel({required String instanceId}) {
    return TreatmentModel(
      id: id!,
      type: TreatmentType.values.firstWhere(
        (e) => e.name == type,
        orElse: () => TreatmentType.MEDICAL_CARE,
      ),
      observation: observation ?? '',
      attachment: attachment,
      instanceId: instanceId,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
