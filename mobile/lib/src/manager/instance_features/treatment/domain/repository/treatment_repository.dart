import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';

import '../entity/treatment.dart';

abstract class TreatmentRepository {
  RepositoryResponse<List<Treatment>> getTreatmentList({
    required String instanceId,
  });

  RepositoryResponse<Treatment> getTreatment({
    required String instanceId,
    required String treatmentId,
  });

  RepositoryResponse<Treatment> createTreatment({
    required String instanceId,
    required TreatmentType type,
    required String observation,
    required String? attachment,
  });

  RepositoryResponse<Treatment> editTreatment({
    required String instanceId,
    required String treatmentId,
    required TreatmentType type,
    required String observation,
    required String? attachment,
  });

  RepositoryResponse<MessageResponse> deleteTreatment({
    required String instanceId,
    required String treatmentId,
  });
}
