import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';

import '../entity/treatment.dart';
import '../repository/treatment_repository.dart';

@injectable
class EditTreatment extends UseCase<Treatment, EditTreatmentParams> {
  final TreatmentRepository _repository;

  EditTreatment(this._repository);

  @override
  call(EditTreatmentParams params) {
    return _repository.editTreatment(
      instanceId: params.instanceId,
      treatmentId: params.treatmentId,
      type: params.type,
      observation: params.observation,
      attachment: params.attachment,
    );
  }
}

class EditTreatmentParams {
  final String instanceId;
  final String treatmentId;
  final TreatmentType type;
  final String observation;
  final String? attachment;

  EditTreatmentParams({
    required this.instanceId,
    required this.treatmentId,
    required this.type,
    required this.observation,
    required this.attachment,
  });
}
