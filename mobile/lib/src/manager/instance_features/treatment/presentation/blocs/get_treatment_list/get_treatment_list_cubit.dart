import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/src/manager/instance_features/treatment/domain/entity/treatment.dart';
import 'package:s3g/src/manager/instance_features/treatment/domain/usecase/get_treatment_list.dart';

part 'get_treatment_list_state.dart';

class GetTreatmentListCubit extends Cubit<GetTreatmentListState> {
  final GetTreatmentList _getTreatmentList;
  final ConnectionChecker _connectionChecker;
  final String instanceId;

  GetTreatmentListCubit(
    this._getTreatmentList,
    this._connectionChecker, {
    required this.instanceId,
  }) : super(GetTreatmentListInitial());

  void getTreatmentList() async {
    emit(GetTreatmentListLoading());

    final isOnline = await _connectionChecker.isOnline();

    final result = await _getTreatmentList(
      GetTreatmentListParams(instanceId),
    );

    result.fold(
      (l) => emit(GetTreatmentListError(message: l.message)),
      (r) => emit(GetTreatmentListLoaded(treatments: r, isOffline: !isOnline)),
    );
  }
}
