import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/src/manager/instance_features/treatment/domain/entity/treatment.dart';
import 'package:s3g/src/manager/instance_features/treatment/domain/usecase/create_treatment.dart';

part 'create_treatment_state.dart';

class CreateTreatmentCubit extends Cubit<CreateTreatmentState> {
  final CreateTreatment _createTreatment;
  final ConnectionChecker _connectionChecker;
  final String instanceId;

  CreateTreatmentCubit(
    this._createTreatment,
    this._connectionChecker, {
    required this.instanceId,
  }) : super(CreateTreatmentInitial());

  void createTreatment({
    required TreatmentType type,
    required String observation,
    String? attachment,
  }) async {
    emit(CreateTreatmentLoading());

    final isOnline = await _connectionChecker.isOnline();

    final result = await _createTreatment(
      CreateTreatmentParams(
        instanceId: instanceId,
        type: type,
        observation: observation,
        attachment: attachment,
      ),
    );

    result.fold(
      (l) => emit(CreateTreatmentFailure(l.message)),
      (r) => emit(CreateTreatmentSuccess(r, isOffline: !isOnline)),
    );
  }
}
