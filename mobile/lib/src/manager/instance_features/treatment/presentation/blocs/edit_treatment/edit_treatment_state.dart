part of 'edit_treatment_cubit.dart';

@immutable
sealed class EditTreatmentState {}

final class EditTreatmentInitial extends EditTreatmentState {}

final class EditTreatmentLoading extends EditTreatmentState {}

final class EditTreatmentSuccess extends EditTreatmentState {
  final Treatment treatment;

  EditTreatmentSuccess(this.treatment);
}

final class EditTreatmentFailure extends EditTreatmentState {
  final String message;

  EditTreatmentFailure(this.message);
}
