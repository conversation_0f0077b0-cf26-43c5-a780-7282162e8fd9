import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:getwidget/getwidget.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/manager/instance/instance.dart';
import 'package:s3g/src/manager/instance_features/treatment/treatment.dart';

import '../blocs/delete_treatment/delete_treatment_cubit.dart';
import '../blocs/get_treatment_list/get_treatment_list_cubit.dart';
import '../widgets/treatment_detail.dart';
import '../widgets/treatment_form.dart';
import '../widgets/treatment_item.dart';

class TreatmentListPage extends StatelessWidget {
  const TreatmentListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final instanceState =
        context.read<InstanceDetailCubit>().state as InstanceDetailLoaded;

    return MultiBlocProvider(
      key: ObjectKey(instanceState.instance.id),
      providers: [
        // Get Diagnostic
        BlocProvider(
          create: (_) => GetTreatmentListCubit(
            getIt(),
            getIt(),
            instanceId: instanceState.instance.id,
          )..getTreatmentList(),
        ),

        // Delete Diagnostic
        BlocProvider(
          create: (_) => DeleteTreatmentCubit(
            getIt(),
            instanceId: instanceState.instance.id,
          ),
        ),

        // Form
        BlocProvider(
          create: (_) => InlineFormCubit<Treatment>(),
        ),
      ],
      child: _TreatmentPageContent(instance: instanceState.instance),
    );
  }
}

class _TreatmentPageContent extends StatelessWidget {
  final InstanceShow instance;

  const _TreatmentPageContent({required this.instance});

  @override
  Widget build(BuildContext context) {
    final inlineForm = context.watch<InlineFormCubit<Treatment>>();

    if (inlineForm.state.enabled) {
      return SingleChildScrollView(
        physics: const ScrollPhysics(),
        padding: bodyPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const PageTitle(title: "Prises en charge"),

            // Form
            TreatmentForm(
              editable: inlineForm.state.editable,
              instance: instance,
              closeEditMode: () {
                inlineForm.disableForm();
              },
            ),
          ],
        ),
      );
    }

    return Scaffold(
      floatingActionButton: instance.status == InstanceStatus.CLOSED
          ? const SizedBox.shrink()
          : FloatingActionButton(
              heroTag: "TreatmentListPage",
              onPressed: () {
                inlineForm.enableForm();
              },
              child: const Icon(Icons.add),
            ),
      body: RefreshIndicator(
        onRefresh: () async {
          context.read<GetTreatmentListCubit>().getTreatmentList();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: bodyPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const PageTitle(title: "Prises en charge"),

              //  List
              BlocBuilder<GetTreatmentListCubit, GetTreatmentListState>(
                builder: (_, state) {
                  switch (state) {
                    case GetTreatmentListInitial() || GetTreatmentListLoading():
                      return const Center(child: GFLoader());

                    case GetTreatmentListError(message: String message):
                      return RetryWidget(
                        message: message,
                        onPressed: () {
                          context
                              .read<GetTreatmentListCubit>()
                              .getTreatmentList();
                        },
                      );

                    case GetTreatmentListLoaded(
                        treatments: List<Treatment> treatments
                      ):
                      if (treatments.isEmpty) {
                        return const EmptyList(
                          message: "Aucune prise en charge trouvée.",
                        );
                      }

                      return ListView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemCount: treatments.length,
                        itemBuilder: (context, index) {
                          final treatment = treatments[index];

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // List title
                              Slidable(
                                endActionPane: ActionPane(
                                  motion: const ScrollMotion(),
                                  children: [
                                    SlidableAction(
                                      onPressed: (_) {
                                        context
                                            .read<InlineFormCubit<Treatment>>()
                                            .enableForm(editable: treatment);
                                      },
                                      backgroundColor: GFColors.INFO,
                                      foregroundColor: Colors.white,
                                      icon: Icons.edit,
                                      label: 'Modifier',
                                    ),
                                    SlidableAction(
                                      onPressed: (_) {
                                        _onDelete(context, treatment);
                                      },
                                      backgroundColor: GFColors.DANGER,
                                      foregroundColor: Colors.white,
                                      icon: Icons.delete,
                                      label: 'Supprimer',
                                    ),
                                  ],
                                ),

                                // Treatment item
                                child: TreatmentItem(
                                  treatment: treatment,
                                  onTap: () {
                                    final parentHeight =
                                        MediaQuery.of(context).size.height *
                                            0.6;

                                    showMaterialModalBottomSheet(
                                      context: context,
                                      shape: bottomSheetRadius,
                                      builder: (context) => TreatmentDetail(
                                        treatment: treatment,
                                        parentHeight: parentHeight,
                                      ),
                                    );
                                  },
                                ),
                              ),

                              // Separator
                              const SizedBox(height: 5),
                              const GreyDivider(),
                            ],
                          );
                        },
                      );

                    // ignore: unreachable_switch_default
                    default:
                  }

                  return Container();
                },
              ),

              // Show delete message
              BlocListener<DeleteTreatmentCubit, DeleteTreatmentState>(
                listener: (_, state) {
                  switch (state) {
                    case DeleteTreatmentLoading():
                      showToast(
                        context,
                        type: ToastType.info,
                        message: "Suppression..",
                      );
                      break;

                    case DeleteTreatmentFailure():
                      showToast(
                        context,
                        type: ToastType.error,
                        message: state.message,
                      );
                      break;

                    case DeleteTreatmentSuccess(message: String message):
                      showToast(
                        context,
                        type: ToastType.success,
                        message: message,
                      );

                      // Refresh the list after deletion
                      context.read<GetTreatmentListCubit>().getTreatmentList();
                      break;
                    default:
                  }
                },
                child: const SizedBox.shrink(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onDelete(BuildContext context, Treatment treatment) async {
    final confirmed = await pressConfirm(context);

    if (confirmed) {
      // ignore: use_build_context_synchronously
      context
          .read<DeleteTreatmentCubit>()
          .deleteTreatment(treatmentId: treatment.id);
    }
  }
}
