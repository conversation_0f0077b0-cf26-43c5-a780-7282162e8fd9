import 'package:flutter/material.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/src/manager/instance_features/treatment/treatment.dart';

import 'attachment_viewer.dart';

class TreatmentDetail extends StatelessWidget {
  final double parentHeight;
  final Treatment treatment;

  const TreatmentDetail({
    super.key,
    required this.treatment,
    required this.parentHeight,
  });

  @override
  Widget build(BuildContext context) {
    return _TreatmentDetailContent(
      parentHeight: parentHeight,
      treatment: treatment,
    );
  }
}

class _TreatmentDetailContent extends StatelessWidget {
  final double parentHeight;
  final Treatment treatment;

  const _TreatmentDetailContent({
    required this.parentHeight,
    required this.treatment,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: parentHeight,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 10,
        ),
        child: _TreatmentDetail(treatment: treatment),
      ),
    );
  }
}

class _TreatmentDetail extends StatelessWidget {
  final Treatment treatment;
  const _TreatmentDetail({required this.treatment});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "Prise en charge: \n${treatment.type.getLabel()}",
              style: Theme.of(context)
                  .textTheme
                  .bodyLarge
                  ?.apply(fontWeightDelta: 1),
            ),

            // File viewer
            if (treatment.attachment != null)
              TreatmentAttachmentViewer(
                attachment: treatment.attachment,
                onDetailPage: true,
              ),
          ],
        ),

        // Some spaces
        columnSizedBox,
        columnSizedBox,

        // Observation
        Expanded(
          child: SingleChildScrollView(
            child: Text(
              treatment.observation,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          ),
        )
      ],
    );
  }
}
