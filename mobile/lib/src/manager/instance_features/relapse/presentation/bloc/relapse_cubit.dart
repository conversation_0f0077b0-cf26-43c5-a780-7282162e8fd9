import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/src/manager/instance_features/relapse/relapse.dart';

import '../../domain/usecase/create_relapse.dart';
import '../../domain/usecase/delete_relapse.dart';
import '../../domain/usecase/get_relapse.dart';
import '../../domain/usecase/update_relapse.dart';

part 'relapse_state.dart';

class RelapseCubit extends Cubit<RelapseState> {
  final CreateRelapse createRelapseUseCase;
  final DeleteRelapse deleteRelapseUseCase;
  final UpdateRelapse updateRelapseUseCase;
  final GetRelapse getRelapseUseCase;
  final ConnectionChecker connectionChecker;
  final String instanceId;

  RelapseCubit(
    this.instanceId, {
    required this.createRelapseUseCase,
    required this.deleteRelapseUseCase,
    required this.updateRelapseUseCase,
    required this.getRelapseUseCase,
    required this.connectionChecker,
  }) : super(RelapseInitial());

  Future<void> getRelapse() async {
    if (isClosed) return;
    emit(RelapseLoading());

    final isOnline = await connectionChecker.isOnline();
    final result = await getRelapseUseCase(
      GetRelapseParams(instanceId: instanceId),
    );

    if (isClosed) return;
    result.fold(
      (_) => emit(RelapseSuccess(
        relapse: null,
        isOffline: !isOnline,
        successType: SuccessType.get,
      )),
      (success) => emit(RelapseSuccess(
        relapse: success,
        isOffline: !isOnline,
        successType: SuccessType.get,
      )),
    );
  }

  Future<void> createRelapse({required String description}) async {
    if (isClosed) return;
    emit(RelapseLoading());

    final isOnline = await connectionChecker.isOnline();
    final result = await createRelapseUseCase(
      CreateRelapseParams(
        instanceId: instanceId,
        description: description,
      ),
    );

    if (isClosed) return;
    result.fold(
      (failure) => emit(RelapseError(message: failure.message)),
      (success) => emit(RelapseSuccess(
        relapse: success,
        isOffline: !isOnline,
        successType: SuccessType.create,
      )),
    );
  }

  Future<void> deleteRelapse() async {
    if (isClosed) return;
    emit(RelapseLoading());

    final isOnline = await connectionChecker.isOnline();
    final result = await deleteRelapseUseCase(
      DeleteRelapseParams(instanceId: instanceId),
    );

    if (isClosed) return;
    result.fold(
      (failure) => emit(RelapseError(message: failure.message)),
      (success) => emit(RelapseSuccess(
        isOffline: !isOnline,
        successType: SuccessType.delete,
      )),
    );
  }

  Future<void> updateRelapse({required String description}) async {
    if (isClosed) return;
    emit(RelapseLoading());

    final isOnline = await connectionChecker.isOnline();
    final result = await updateRelapseUseCase(
      UpdateRelapseParams(
        instanceId: instanceId,
        description: description,
      ),
    );

    if (isClosed) return;
    result.fold(
      (failure) => emit(RelapseError(message: failure.message)),
      (success) => emit(RelapseSuccess(
        relapse: success,
        isOffline: !isOnline,
        successType: SuccessType.update,
      )),
    );
  }
}
