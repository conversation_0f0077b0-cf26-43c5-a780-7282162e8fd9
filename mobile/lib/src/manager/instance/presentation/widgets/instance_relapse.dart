import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';
import 'package:intl/intl.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/src/manager/instance/instance.dart';
import 'package:s3g/src/manager/instance_features/relapse/relapse.dart';

class InstanceRelapse extends StatefulWidget {
  final Relapse? relapse;
  final void Function() closeEditMode;

  const InstanceRelapse({
    super.key,
    required this.relapse,
    required this.closeEditMode,
  });

  @override
  State<InstanceRelapse> createState() => _InstanceRelapseState();
}

class _InstanceRelapseState extends State<InstanceRelapse> {
  bool editMode = false;

  @override
  Widget build(BuildContext context) {
    final relapse = widget.relapse;

    if (relapse != null && !editMode) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Back button
              Padding(
                padding: const EdgeInsets.only(top: 7),
                child: GFIconButton(
                  color: Colors.grey[500] ?? Colors.grey,
                  size: GFSize.SMALL,
                  type: GFButtonType.outline,
                  icon: Icon(Icons.arrow_back, color: Colors.grey[500]),
                  onPressed: widget.closeEditMode,
                ),
              ),

              // Some space
              const SizedBox(width: 20),

              // Relapse date
              Expanded(
                child: DetailTitle(
                  // leading: const Icon(Icons.trending_down_outlined),
                  title: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Relapse date
                      const Text(
                        "Rechute signalé le",
                        style: TextStyle(fontSize: 18.0),
                      ),

                      // Relapse date
                      GFIconButton(
                        size: GFSize.SMALL,
                        color: Theme.of(context).colorScheme.primary,
                        type: GFButtonType.outline,
                        icon: const Icon(Icons.edit),
                        onPressed: () {
                          setState(() {
                            editMode = true;
                          });
                        },
                      )
                    ],
                  ),
                  subtitle: Text(
                    DateFormat.yMMMd('fr').format(
                      relapse.createdAt.toLocal(),
                    ),
                    style: const TextStyle(
                      fontSize: 16.0,
                      color: Colors.red,
                    ),
                  ),
                ),
              ),
            ],
          ),
          columnSizedBox,
          const GreyDivider(),

          // Description
          columnSizedBox,
          Text(
            relapse.description,
            style: const TextStyle(fontSize: 18.0),
          ),
          columnSizedBox,
          columnSizedBox,

          BlocBuilder<RelapseCubit, RelapseState>(
            builder: (_, state) {
              switch (state) {
                case RelapseLoading():
                  return const GFLoader(size: GFSize.SMALL);
                default:
              }

              return GFButton(
                color: Theme.of(context).colorScheme.primary,
                onPressed: () async {
                  if (await pressConfirm(context)) {
                    return _deleteRelapse();
                  }
                },
                child: const Text("Supprimer le signalement"),
              );
            },
          ),
        ],
      );
    }

    return _RelapsePageForm(
      key: ValueKey(editMode),
      relapse: relapse,
      closeEditMode: () {
        if (relapse == null) {
          widget.closeEditMode();
        }

        setState(() {
          editMode = false;
        });
      },
    );
  }

  void _deleteRelapse() async {
    await context.read<RelapseCubit>().deleteRelapse();

    if (mounted) {
      context.read<InstanceDetailCubit>().getInstance();
      context
          .read<RefreshDataBloc>()
          .add(RefreshDataHealthCenterInstancesEvent());
    }

    setState(() {
      editMode = false;
    });

    widget.closeEditMode();
  }
}

class _RelapsePageForm extends StatefulWidget {
  final Relapse? relapse;
  final void Function() closeEditMode;

  const _RelapsePageForm({
    super.key,
    required this.relapse,
    required this.closeEditMode,
  });

  @override
  State<_RelapsePageForm> createState() => _RelapsePageFormState();
}

class _RelapsePageFormState extends State<_RelapsePageForm> {
  final _textController = TextEditingController();

  @override
  void initState() {
    final relapse = widget.relapse;
    _textController.text = relapse?.description ?? '';

    super.initState();
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final relapseBlocState = context.watch<RelapseCubit>().state;

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        const Text("Entrez votre texte"),

        const SizedBox(height: 10),

        // Text input
        TextField(
          enabled: true,
          controller: _textController,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            labelText: 'Description',
          ),
          keyboardType: TextInputType.multiline,
          minLines: 5,
          maxLines: 20,
        ),

        columnSizedBox,

        // Listener and show toast
        BlocListener<RelapseCubit, RelapseState>(
          listener: (_, state) {
            if (state case RelapseError(message: String message)) {
              showToast(context, type: ToastType.error, message: message);
            }
          },
          child: const SizedBox.shrink(),
        ),

        // Close and save buttons
        FormButtons(
          loading: relapseBlocState is RelapseLoading,
          onSubmit: _save,
          onCancel: widget.closeEditMode,
        )
      ],
    );
  }

  void _save() async {
    // Create or update relapse status
    if (widget.relapse != null) {
      // Update existing relapse
      await context
          .read<RelapseCubit>()
          .updateRelapse(description: _textController.value.text);
    } else {
      // Create new relapse
      await context
          .read<RelapseCubit>()
          .createRelapse(description: _textController.value.text);

      // Refresh instance
      if (mounted) {
        context.read<InstanceDetailCubit>().getInstance();
        context.read<RelapseCubit>().getRelapse();
        context
            .read<RefreshDataBloc>()
            .add(RefreshDataHealthCenterInstancesEvent());
      }
    }

    widget.closeEditMode();
  }
}
