import 'dart:developer';

import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/sync/sync_service.dart';
import 'package:s3g/src/manager/instance/data/local/instance_local_datasource.dart';
import 'package:s3g/src/manager/instance/data/local/models/instance_local_model.dart';
import 'package:s3g/src/manager/instance/domain/entity/instance.dart';

import '../../domain/repository/instance_repository.dart';
import '../remote/instance_remote_datasource.dart';

@Injectable(as: InstanceRepository)
class InstanceRepositoryImpl implements InstanceRepository {
  final InstanceRemoteDataSource _remoteDataSource;
  final InstanceLocalDataSource _localDataSource;
  final ConnectionChecker _connectionChecker;
  final SyncService _syncService;

  InstanceRepositoryImpl({
    required InstanceRemoteDataSource remoteDataSource,
    required InstanceLocalDataSource localDataSource,
    required ConnectionChecker connectionChecker,
    required SyncService syncService,
  })  : _remoteDataSource = remoteDataSource,
        _localDataSource = localDataSource,
        _connectionChecker = connectionChecker,
        _syncService = syncService;

  @override
  RepositoryResponse<Paginated<Instance>> getAllInstance({
    int? page,
    String? search,
  }) {
    return requestHelper(() => _remoteDataSource.getAllInstance(
          page: page,
          search: search,
        ));
  }

  @override
  RepositoryResponse<InstanceShow> getInstance(
      {required String instanceId}) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to get fresh data from server
        final response = await requestHelper(
          () => _remoteDataSource.getInstance(instanceId: instanceId),
        );

        // If successful, update local storage
        if (response.isRight()) {
          final instanceShow = response.getOrElse((l) => throw l);

          final updatedLocalModel =
              InstanceLocalModel.fromInstanceShow(instanceShow);

          await _localDataSource.updateInstanceFromServer(updatedLocalModel);
        }

        return response;
      } else {
        InstanceLocalModel? localInstance =
            await _localDataSource.getInstanceById(instanceId);

        // Device is offline, return from local storage
        if (localInstance != null && !localInstance.isDeleted) {
          // Use the toInstanceShow method to convert local model
          final instanceShow = localInstance.toInstanceShow();
          return Right(instanceShow);
        } else {
          return Left(
              ServerFailure('Instance introuvable dans le stockage local'));
        }
      }
    } catch (e) {
      log('Error getting instance: $e');
      return Left(ServerFailure('Une erreur s\'est produite: ${e.toString()}'));
    }
  }

  @override
  RepositoryResponse<Paginated<Instance>> getInstances({
    required String healthCenterId,
    int? page,
    String? search,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // First, sync any pending local changes to avoid overwriting them
        try {
          await _syncService.syncManagerInstances();
        } catch (e) {
          log('Warning: Failed to sync pending instances before fetch: $e');
          // Continue with fetch even if sync fails
        }

        // Try to get from server
        final response = await requestHelper(
          () => _remoteDataSource.getInstances(
            healthCenterId: healthCenterId,
            page: page,
            search: search,
          ),
        );

        // If successful and it's the first page, update local storage
        if (response.isRight() && (page == null || page == 1)) {
          final paginated = response.getOrElse((l) => throw l);

          // Update each instance properly to handle existing records
          for (var instance in paginated.data) {
            final localInstance = InstanceLocalModel.fromEntity(instance);
            _localDataSource.updateInstanceFromServer(localInstance);
          }
        }

        return response;
      } else {
        // Device is offline, get from local storage
        log('Device offline, getting instances from local storage');

        List<InstanceLocalModel> localInstances;

        if (search != null && search.isNotEmpty) {
          localInstances = await _localDataSource.searchInstances(
            search,
            healthCenterId: healthCenterId,
          );
        } else {
          localInstances =
              await _localDataSource.getInstancesByHealthCenter(healthCenterId);
        }

        final instances = localInstances
            .where((i) => !i.isDeleted)
            .map((i) => i.toEntity())
            .toList();

        // Create a paginated response
        final paginatedResponse = Paginated<Instance>.create(instances);

        return Right(paginatedResponse);
      }
    } catch (e) {
      log('Error getting instances: $e');
      return Left(ServerFailure('Une erreur s\'est produite: ${e.toString()}'));
    }
  }

  @override
  RepositoryResponse<Paginated<Instance>> getInstancesRelapsed({
    required String healthCenterId,
    int? page,
    String? search,
  }) {
    return requestHelper(
      () => _remoteDataSource.getInstancesRelapsed(
        healthCenterId: healthCenterId,
        page: page,
        search: search,
      ),
    );
  }

  @override
  RepositoryResponse<InstanceShow> createInstance({
    required String healthCenterId,
    required String survivorCode,
    required InstanceType type,
    String? description,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to create on server first
        final response = await requestHelper(
          () => _remoteDataSource.createInstance(
            healthCenterId: healthCenterId,
            survivorCode: survivorCode,
            type: type,
            description: description,
          ),
        );

        // If successful, save to local storage as synced
        if (response.isRight()) {
          final instanceShow = response.getOrElse((l) => throw l);
          final localModel = InstanceLocalModel.fromInstanceShow(instanceShow);
          await _localDataSource.updateInstanceFromServer(localModel);
        }

        return response;
      } else {
        // Device is offline, create locally
        log('Device offline, creating instance locally');

        final offlineInstance = InstanceLocalModel.createOffline(
          type: type,
          survivorCode: survivorCode,
          healthCenterId: healthCenterId,
          description: description,
        );

        await _localDataSource.saveInstance(offlineInstance);

        // Return as InstanceShow using the conversion method
        final instanceShow = offlineInstance.toInstanceShow();
        return Right(instanceShow);
      }
    } catch (e) {
      log('Error creating instance: $e');
      return Left(ServerFailure('Une erreur s\'est produite: ${e.toString()}'));
    }
  }

  @override
  RepositoryResponse<InstanceShow> editInstance({
    required String healthCenterId,
    required String instanceId,
    required InstanceStatus status,
    String? description,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to update on server first
        final response = await requestHelper(
          () => _remoteDataSource.editInstance(
            healthCenterId: healthCenterId,
            instanceId: instanceId,
            status: status,
            description: description,
          ),
        );

        // If successful, update local storage
        if (response.isRight()) {
          final instanceShow = response.getOrElse((l) => throw l);
          final localModel = InstanceLocalModel.fromInstanceShow(instanceShow);
          await _localDataSource.updateInstanceFromServer(localModel);
        }

        return response;
      } else {
        // Device is offline, update locally
        log('Device offline, updating instance locally');

        // Find the instance
        InstanceLocalModel? localInstance =
            await _localDataSource.getInstanceById(instanceId);

        if (localInstance == null) {
          return Left(ServerFailure('Instance introuvable'));
        }

        // Update the instance by creating a new one with updated fields
        await _localDataSource.updateInstanceContent(
          localInstance.localId,
          status: status.name,
          description: description,
        );

        // Get updated instance
        final updatedInstance =
            await _localDataSource.getInstanceByLocalId(localInstance.localId);

        if (updatedInstance == null) {
          return Left(
              ServerFailure('Impossible de récupérer l\'instance mise à jour'));
        }

        // Trigger sync in background
        _syncService.syncManagerInstances().catchError((e) {
          log('Failed to sync instance immediately: $e');
        });

        // Use the toInstanceShow method for conversion
        final instanceShow = updatedInstance.toInstanceShow();
        return Right(instanceShow);
      }
    } catch (e) {
      log('Error updating instance: $e');
      return Left(ServerFailure('Une erreur s\'est produite: ${e.toString()}'));
    }
  }

  @override
  RepositoryResponse<MessageResponse> deleteInstance({
    required String healthCenterId,
    required String instanceId,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to delete on server first
        final response = await requestHelper(
          () => _remoteDataSource.deleteInstance(
            healthCenterId: healthCenterId,
            instanceId: instanceId,
          ),
        );

        // If successful, delete from local storage
        if (response.isRight()) {
          await _localDataSource.deleteInstanceById(instanceId);
        }

        return response;
      } else {
        // Device is offline, mark as deleted locally
        log('Device offline, marking instance as deleted locally');

        // Find the instance (by server ID or local ID)
        InstanceLocalModel? localInstance =
            await _localDataSource.getInstanceById(instanceId);

        if (localInstance == null) {
          return Left(ServerFailure('Instance introuvable'));
        }

        // Mark as deleted
        await _localDataSource.markInstanceAsDeleted(localInstance.localId);

        return Right(MessageResponse(message: 'Instance deleted locally'));
      }
    } catch (e) {
      log('Error deleting instance: $e');
      return Left(ServerFailure('Une erreur s\'est produite: ${e.toString()}'));
    }
  }
}
