import 'dart:convert';

import 'package:objectbox/objectbox.dart';
import 'package:s3g/src/manager/questionnaire/questionnaire.dart';

@Entity()
class QuestionnaireLocalModel {
  @Id()
  int oid = 0;

  @Index()
  @Unique()
  String id;

  String question;
  String type;
  String? hint;
  String choicesJson;

  @Property(type: PropertyType.date)
  DateTime createdAt;

  @Property(type: PropertyType.date)
  DateTime updatedAt;

  QuestionnaireLocalModel({
    required this.id,
    required this.question,
    required this.type,
    required this.choicesJson,
    this.hint,
    required this.createdAt,
    required this.updatedAt,
  });

  factory QuestionnaireLocalModel.fromEntity(Questionnaire questionnaire) {
    final choices = (questionnaire.choices ?? [])
        .map((e) => QuestionnaireChoiceModel.fromEntity(e).toJson())
        .toList();

    final choicesJson = jsonEncode(choices);

    return QuestionnaireLocalModel(
      id: questionnaire.id,
      question: questionnaire.question,
      type: questionnaire.type.name,
      hint: questionnaire.hint,
      choicesJson: choicesJson,
      createdAt: questionnaire.createdAt,
      updatedAt: questionnaire.updatedAt,
    );
  }

  Questionnaire toEntity() {
    final choices = jsonDecode(choicesJson) as List<dynamic>;

    final choicesModels =
        choices.map((e) => QuestionnaireChoiceModel.fromJson(e)).toList();

    return Questionnaire(
      id: id,
      question: question,
      type: QuestionnaireResponseType.values.firstWhere(
        (e) => e.name == type,
        orElse: () => QuestionnaireResponseType.TEXT,
      ),
      hint: hint,
      choices: choicesModels,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
