import 'dart:developer';

import 'package:injectable/injectable.dart';
import 'package:s3g/core/objectbox/objectbox.dart';
import 'package:s3g/src/manager/health_center/data/local/models/health_center_local_model.dart';

abstract class HealthCenterLocalDataSource {
  Future<List<HealthCenterLocalModel>> getAllHealthCenters();
  Future<HealthCenterLocalModel?> getHealthCenterById(String id);
  Future<void> saveHealthCenter(HealthCenterLocalModel healthCenter);
  Future<void> saveHealthCenters(List<HealthCenterLocalModel> healthCenters);
  Future<void> updateHealthCenterFromServer(
      HealthCenterLocalModel serverHealthCenter);
  Future<void> deleteHealthCenter(String id);
  Future<void> clearAllHealthCenters();
  Future<List<HealthCenterLocalModel>> searchHealthCenters(String query);
}

@Injectable(as: HealthCenterLocalDataSource)
class HealthCenterLocalDataSourceImpl implements HealthCenterLocalDataSource {
  final ObjectBox objectBox;

  HealthCenterLocalDataSourceImpl({required this.objectBox});

  Box<HealthCenterLocalModel> get _box =>
      objectBox.store.box<HealthCenterLocalModel>();

  @override
  Future<List<HealthCenterLocalModel>> getAllHealthCenters() async {
    try {
      final healthCenters = await _box.getAllAsync();
      return healthCenters;
    } catch (e) {
      log('Error getting all health centers: $e');
      return [];
    }
  }

  @override
  Future<HealthCenterLocalModel?> getHealthCenterById(String id) async {
    try {
      final query = _box.query(HealthCenterLocalModel_.id.equals(id)).build();
      final healthCenter = await query.findFirstAsync();
      query.close();
      return healthCenter;
    } catch (e) {
      log('Error getting health center by id: $e');
      return null;
    }
  }

  @override
  Future<void> saveHealthCenter(HealthCenterLocalModel healthCenter) async {
    try {
      HealthCenterLocalModel? existing;

      // Check if a health center with this ID already exists
      if (healthCenter.id.isNotEmpty) {
        final query = _box
            .query(HealthCenterLocalModel_.id.equals(healthCenter.id))
            .build();

        existing = await query.findFirstAsync();
        query.close();
      }

      if (existing != null) {
        // Update the existing record, preserving the ObjectBox ID
        existing.name = healthCenter.name;
        existing.address = healthCenter.address;
        existing.phone = healthCenter.phone;
        existing.servicesOffered = healthCenter.servicesOffered;
        existing.aps = healthCenter.aps;
        existing.companions = healthCenter.companions;
        existing.instances = healthCenter.instances;
        existing.relapses = healthCenter.relapses;
        existing.responsibility = healthCenter.responsibility;
        existing.healthZoneJson = healthCenter.healthZoneJson;
        existing.createdAt = healthCenter.createdAt;
        existing.updatedAt = healthCenter.updatedAt;
        existing.lastSyncedAt = healthCenter.lastSyncedAt;
        await _box.putAsync(existing);
        log('Updated existing health center: ${healthCenter.id}');
        return;
      }

      // No existing record found, save as new
      await _box.putAsync(healthCenter);
      log('Saved health center: ${healthCenter.id}');
    } catch (e) {
      log('Error saving health center: $e');
      rethrow;
    }
  }

  @override
  Future<void> saveHealthCenters(
    List<HealthCenterLocalModel> healthCenters,
  ) async {
    try {
      // Process each health center individually to handle updates
      for (final healthCenter in healthCenters) {
        await saveHealthCenter(healthCenter);
      }
      log('Saved ${healthCenters.length} health centers');
    } catch (e) {
      log('Error saving health centers: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateHealthCenterFromServer(
    HealthCenterLocalModel serverHealthCenter,
  ) async {
    try {
      // Check if we have a local version by ID
      final existing = await getHealthCenterById(serverHealthCenter.id);

      if (existing != null) {
        // Only update if server version is newer
        if (serverHealthCenter.updatedAt.isAfter(existing.updatedAt)) {
          existing.updateFromServer(serverHealthCenter.toEntity());
          await _box.putAsync(existing);
          log('Updated health center from server: ${serverHealthCenter.id}');
        }
      } else {
        // Save new health center from server
        await saveHealthCenter(serverHealthCenter);
        log('Added new health center from server: ${serverHealthCenter.id}');
      }
    } catch (e) {
      log('Error updating health center from server: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteHealthCenter(String id) async {
    try {
      final healthCenter = await getHealthCenterById(id);
      if (healthCenter != null) {
        await _box.removeAsync(healthCenter.oid);
        log('Deleted health center: $id');
      }
    } catch (e) {
      log('Error deleting health center: $e');
      rethrow;
    }
  }

  @override
  Future<void> clearAllHealthCenters() async {
    try {
      await _box.removeAllAsync();
      log('Cleared all health centers from local storage');
    } catch (e) {
      log('Error clearing health centers: $e');
      rethrow;
    }
  }

  @override
  Future<List<HealthCenterLocalModel>> searchHealthCenters(String query) async {
    try {
      final lowerQuery = query.toLowerCase();
      final allHealthCenters = await getAllHealthCenters();

      return allHealthCenters.where((hc) {
        return hc.name.toLowerCase().contains(lowerQuery) ||
            hc.address.toLowerCase().contains(lowerQuery) ||
            hc.phone.toLowerCase().contains(lowerQuery) ||
            (hc.servicesOffered?.toLowerCase().contains(lowerQuery) ?? false);
      }).toList();
    } catch (e) {
      log('Error searching health centers: $e');
      return [];
    }
  }
}
