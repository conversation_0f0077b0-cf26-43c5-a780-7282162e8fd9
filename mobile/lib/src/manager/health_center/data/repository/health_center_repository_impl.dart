import 'dart:developer';

import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';

import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/src/manager/health_center/data/local/health_center_local_datasource.dart';
import 'package:s3g/src/manager/health_center/data/local/models/health_center_local_model.dart';
import 'package:s3g/src/manager/health_center/data/remote/health_center_remote_datasource.dart';

import 'package:s3g/src/manager/health_center/domain/entity/health_center.dart';

import '../../domain/repository/health_center_repository.dart';

@Injectable(as: HealthCenterRepository)
class HealthCenterRepositoryImpl extends HealthCenterRepository {
  final HealthCenterRemoteDataSource _remoteDataSource;
  final HealthCenterLocalDataSource _localDataSource;
  final ConnectionChecker _connectionChecker;

  HealthCenterRepositoryImpl(
    this._remoteDataSource,
    this._localDataSource,
    this._connectionChecker,
  );

  @override
  Future<Either<Failure, List<HealthCenter>>> getHealthCenters() async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to get from server
        final response = await requestHelper(
          () => _remoteDataSource.getHealthCenters(),
        );

        // If successful, update local storage
        if (response.isRight()) {
          final healthCenters = response.getOrElse((l) => throw l);

          // Save to local storage for offline access
          final localHealthCenters = healthCenters
              .map((hc) => HealthCenterLocalModel.fromEntity(hc))
              .toList();

          _localDataSource.saveHealthCenters(localHealthCenters);
        }

        return response;
      } else {
        // Device is offline, get from local storage
        log('Device offline, getting health centers from local storage');

        final localHealthCenters = await _localDataSource.getAllHealthCenters();

        if (localHealthCenters.isEmpty) {
          return Left(CacheFailure(
            'Aucune donnée disponible hors ligne',
          ));
        }

        final healthCenters =
            localHealthCenters.map((local) => local.toEntity()).toList();

        return Right(healthCenters);
      }
    } catch (e) {
      log('Error getting health centers: $e');
      return Left(ServerFailure('Une erreur s\'est produite'));
    }
  }

  @override
  Future<Either<Failure, List<HealthCenter>>> searchHealthCenters(
      String query) async {
    try {
      // Always search locally for better performance
      final localHealthCenters =
          await _localDataSource.searchHealthCenters(query);

      if (localHealthCenters.isEmpty) {
        return Left(CacheFailure(
          'Aucun centre de santé trouvé',
        ));
      }

      final healthCenters =
          localHealthCenters.map((local) => local.toEntity()).toList();

      return Right(healthCenters);
    } catch (e) {
      log('Error searching health centers: $e');
      return Left(DatabaseFailure('Erreur lors de la recherche'));
    }
  }
}
